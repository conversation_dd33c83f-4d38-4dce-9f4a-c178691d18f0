import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { bindDatasetItems } from './propsConfig/bindDataset/bindDataset';
import { componentStyleItems } from './propsConfig/componentStyle';
import { dataConfigItems } from './propsConfig/dataConfig';

export const JNDualYBarChartProps: IPublicTypeFieldConfig[] = [
  // 绑定数据集
  {
    type: 'group',
    name: 'bindDataset',
    title: '数据集配置',
    items: bindDatasetItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局
  {
    type: 'group',
    name: 'componentStyle',
    title: '组件样式',
    items: componentStyleItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 数据字段配置
  {
    type: 'group',
    name: 'dataConfig',
    title: '数据字段配置',
    items: dataConfigItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局样式
  {
    type: 'group',
    name: 'style',
    title: '组件全局样式',
    items: [
      {
        type: 'field',
        name: 'style',
        title: '组件全局样式',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
        extraProps: {
          display: 'plain',
        },
      },
    ],
    extraProps: {
      display: 'accordion',
    },
  },
];

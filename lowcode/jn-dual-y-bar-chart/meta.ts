import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNDualYBarChartSnippets } from './snippets';
import { JNDualYBarChartProps } from './props';

const JNDualYBarChartMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNDualYBarChart',
  group: '默认分组',
  title: '双Y轴横向柱状图',
  icon: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNDualYBarChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNDualYBarChartProps,
    component: {},
  },
  snippets: JNDualYBarChartSnippets,
};

export default JNDualYBarChartMeta;

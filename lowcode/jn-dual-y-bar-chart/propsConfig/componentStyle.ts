import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const componentStyleItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'componentStyle.title',
    title: '标题名称',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
    },
  },
  // {
  //   type: 'field',
  //   name: 'componentStyle.route',
  //   title: '跳转链接',
  //   setter: 'StringSetter',
  //   extraProps: {
  //     display: 'inline',
  //     condition: (target) => {
  //       return !!target.getProps().getPropValue('componentStyle.routeControl');
  //     },
  //   },
  // },
  // {
  //   type: 'field',
  //   name: 'componentStyle.routeControl',
  //   title: '跳转开关',
  //   setter: 'BoolSetter',
  //   extraProps: {
  //     defaultValue: false,
  //     display: 'inline',
  //   },
  // },
  {
    type: 'field',
    name: 'componentStyle.xAxisName',
    title: 'X轴名称',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.barWidth',
    title: '柱状宽度',
    setter: 'NumberSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.topGrid',
    title: '顶部间距',
    setter: {
      componentName: 'StringSetter',
      props: {
        placeholder: '请输入数字，以(px/%)作为单位',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.bottomGrid',
    title: '底部间距',
    setter: {
      componentName: 'StringSetter',
      props: {
        placeholder: '请输入数字，以(px/%)作为单位',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.leftGrid',
    title: '左侧间距',
    setter: {
      componentName: 'StringSetter',
      props: {
        placeholder: '请输入数字，以(px/%)作为单位',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.rightGrid',
    title: '右侧间距',
    setter: {
      componentName: 'StringSetter',
      props: {
        placeholder: '请输入数字，以(px/%)作为单位',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.visualMap',
    title: '视觉映射',
    setter: 'BoolSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.mapStartName',
    title: '映射起始名',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
      condition: (target) => {
        return !!target.getProps().getPropValue('componentStyle.visualMap');
      },
    },
  },
  {
    type: 'field',
    name: 'componentStyle.mapEndName',
    title: '映射结束名',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
      condition: (target) => {
        return !!target.getProps().getPropValue('componentStyle.visualMap');
      },
    },
  },
  {
    type: 'field',
    name: 'componentStyle.mapColorList',
    title: '颜色列表',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'color',
                  title: '颜色',
                  description: '颜色',
                  isRequired: true,
                  setter: 'ColorSetter',
                },
              ],
            },
          },
        },
        initialValue: {
          color: '#FFFFFF',
        },
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
];

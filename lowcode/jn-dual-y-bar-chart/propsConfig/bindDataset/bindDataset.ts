import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { initComponentConfig } from './initConfig';

export const bindDatasetItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'bindDataset',
    title: '数据绑定',
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        hasClear: true,
        showSearch: true,
        followTrigger: true,
        placeholder: '请选择数据集',
        filterType: 'List',
      },
    },
    extraProps: {
      display: 'inline',
      setValue: (target, value) => {
        initComponentConfig(target, value, 'set');
        return value;
      },
    },
  },
];

import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const dataConfigItems: IPublicTypeFieldConfig[] = [
  // {
  //   name: 'dataFormatType',
  //   title: '数据格式',
  //   display: 'inline',
  //   setter: {
  //     componentName: 'SelectSetter',
  //     props: {
  //       // options: DEFAULT_DATA_FORMAT_TYPE,
  //       // options: DEFAULT_DATA_FORMAT_TYPE,
  //     },
  //   },
  // },
  {
    type: 'field',
    name: 'dataConfig.xAxis',
    title: 'X轴字段',
    setter: {
      componentName: 'SelectSetter',
      props: (target) => {
        const allFieldList = target.getProps().getPropValue('allHeadList');
        const listExist = allFieldList && allFieldList?.length;

        return {
          options: listExist ? allFieldList : [],
          mode: 'single',
          showSearch: true,
          hasClear: true,
        };
      },
    },
  },
  {
    type: 'field',
    name: 'dataConfig.leftYAxis',
    title: '左Y轴字段',
    setter: {
      componentName: 'SelectSetter',
      props: (target) => {
        const allFieldList = target.getProps().getPropValue('allHeadList');
        const listExist = allFieldList && allFieldList?.length;

        return {
          options: listExist ? allFieldList : [],
          mode: 'single',
          showSearch: true,
          hasClear: true,
        };
      },
    },
  },
  {
    type: 'field',
    name: 'dataConfig.rightYAxis',
    title: '右Y轴字段',
    setter: {
      componentName: 'SelectSetter',
      props: (target) => {
        const allFieldList = target.getProps().getPropValue('allHeadList');
        const listExist = allFieldList && allFieldList?.length;

        return {
          options: listExist ? allFieldList : [],
          mode: 'single',
          showSearch: true,
          hasClear: true,
        };
      },
    },
  },
];

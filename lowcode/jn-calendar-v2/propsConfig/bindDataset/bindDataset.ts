import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const bindDatasetItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'specificDataset.bindDataset',
    title: '数据绑定',
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        hasClear: true,
        showSearch: true,
        followTrigger: true,
        placeholder: '请选择数据集',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
];



import { creatFilter, updateFilter } from "../../src/services/cards/cardDataService";
import {  setGlobalTimeFilterId, setGlobalTimeFilterRange } from "../utils/config";
import { columnsField, getUrlParam } from "./columns-field";


if (!window.pageConfig || !window.pageConfig.timeRange) {
  window.pageConfig = {
    timeRange: {
      key: 'days7',
    }
  }
}

export const props = [
  {
    type: 'select',
    name: '',
    title: '组件样式',
    display: 'accordion',
    setter: (field, target) => {
      return {
        componentName: 'ObjectSetter',
        display: 'inline',
        props: {
          config: {
            items: [
              {
                name: "type",
                title: "观远过滤器",
                extraProps: {
                  // display: 'line',
                  setValue: (target, value) => {
                    // 创建一个北四的过滤器并将id设置到北四
                    const filterId =  target.parent.getPropValue('filterId');
                    let runFn = creatFilter
                    const params = {
                      cardId: value,
                      pageId: getUrlParam('id') || '1' // 临时写死
                    }
                    if(filterId){
                      runFn = updateFilter;
                      params.id = filterId;
                    }
                    runFn(params).then((result: any) => {
                      target.parent.setPropValue('filterId', result.id);
                      target.parent.setPropValue('tiemFrame', ['昨天', '上周', '本月至昨天', '日', '周', '月', '年', '自定义']);
                      setGlobalTimeFilterId(result.id)
                      setGlobalTimeFilterRange({
                        key: 'BT',
                        value: 'yesterday',
                        filterId: result.id
                      })
                    })
                  }
                },
                setter: [
                  {
                    componentName: "CascaderSelectSetter",
                    props: {

                    }
                  }
                ]
              },
              {
                name: 'filterId',
                extraProps: {
                  // display: 'line',
                  setValue: (target, value) => {
                    setGlobalTimeFilterId(value)
                  }
                },
                title: '筛选器ID',
                initialValue: false,
                setter: 'StringSetter',
              },
              {
                name: 'tiemFrame',
                title: '时间范围',
                display: 'inline',
                setter: 'TiemCheckboxSetter',
              },
              {
                name: 'defalutKey',
                title: '默认值',
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    setGlobalTimeFilterRange({
                      key: 'BT',
                      value,
                      filterId
                    })
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      {
                        value: 'yesterday',
                        title: '昨天',
                      },
                      {
                        value: 'today',
                        title: '今日',
                      },
                      {
                        value: 'days7',
                        title: '近7天',
                      },
                      {
                        value: 'days30',
                        title: '近30天',
                      },
                      {
                        value: 'lastWeek',
                        title: '上周',
                      },
                      {
                        value: 'thisWeekToYesterday',
                        title: '本周至昨天',
                      },
                      {
                        value: 'thisMonthToYesterday',
                        title: '本月至昨天',
                      },
                      {
                        value: 'thisYearToYesterday',
                        title: '本年至昨天',
                      },
                      {
                        value: 'presaleToYesterday',
                        title: '预售至昨天',
                      },
                      {
                        value: 'saleToYesterday',
                        title: '正式至昨天'
                      },
                      {
                        value: 'futureSeven',
                        title: '未来7天'
                      },
                      {
                        value: 'futureThirty',
                        title: '未来30天'
                      },
                      {
                        value: 'futureNinety',
                        title: '未来90天'
                      },
                      {
                        value: 'futureThirtyEighty',
                        title: '未来180天',
                      },
                      {
                        value: 'doubleEleven',
                        title: '双11备货',
                      },
                      {
                        value: 'doubleElevenPresale',
                        title: '双11预售起',
                      },
                      {
                        value: 'doubleElevenOfficial',
                        title: '双11正式起',
                      },
                      {
                        value: 'doubleTwelve',
                        title: '双12备货',
                      },
                    ],
                  },
                }
              },
              {
                name: 'disabledDateState',
                title: '开启未来时间',
                setter: 'BoolSetter',
              },
              {
                name: 'styleType',
                title: '布局方式',
                initialValue: '通用',
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: { options: ['通用', '简约'] },
                },
              },
              {
                name: 'title',
                title: '组件标题',
                setter: 'StringSetter',
                condition: (target) => {
                  return target.parent.getPropValue('styleType') === '通用';
                },
              },
              {
                name: 'fontSize',
                title: '标题大小(px)',
                setter: 'NumberSetter',
                condition: (target) => {
                  return target.parent.getPropValue('styleType') === '通用';
                },
              },
            ],
          },
        },
      }
    },
  }, columnsField, {
    name: "style",
    title: "行内样式",
    display: 'accordion',
    setter: {
      componentName: 'StyleSetter',
    },
  },]


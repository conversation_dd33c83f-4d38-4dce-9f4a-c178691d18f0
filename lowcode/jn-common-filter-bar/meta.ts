import { IComponentDescription } from '../types/index';
import { props } from './props';

function JNFilterBarFilter(prop: any) {
  const ignorePropNames: string[] = ['dataSource'];
  return !ignorePropNames.includes(prop?.name);
}

export const JNCommonFilterBarMeta: IComponentDescription = {
  componentName: 'JNCommonFilterBar',
  isHidden: true,
  title: '通用筛选器',
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b025.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNCommonFilterBar',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
      disableBehaviors: ['copy', 'move'],
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '通用筛选器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b025.png',
      schema: {
        componentName: 'JNCommonFilterBar',
        props: {
          settingButtons: true,
          columns: [],
          styleType: '通用',
          defalutKey: 'yesterday',
        },
      },
    },
  ],
};

export default JNCommonFilterBarMeta;

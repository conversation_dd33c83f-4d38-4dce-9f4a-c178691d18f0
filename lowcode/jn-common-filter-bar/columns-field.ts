import { creatFilter,updateFilter } from "../../src/services/cards/cardDataService";
import _ from 'lodash';
import { setGlobalTimeFilterRange, setGlobalTimeRange } from '../utils/config';
import effectRanges from '../common/filters/effectRanges';
// 提取url参数
export function getUrlParam(name) {
  return (
    decodeURIComponent(
      (new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`, 'i').exec(location.search) || [
        undefined,
        '',
      ])[1].replace(/\+/g, '%20'),
    ) || null
  );
}

const map = {
  time: 'BT',
  singleTime: 'BT',
  select: 'IN',
  multipleSelect: 'IN',
  treeSelect: 'IN',
  interval: 'BT',
}

export const columnsField = {
  type: 'field',
  name: 'filters',
  title: '筛选器列表',
  extraProps: {
    display: 'accordion',
  },
  setter: {
    componentName: 'ArraySetter',
    props: {
      itemSetter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: "type",
                title: "观远过滤器",
                isRequired: true,
                extraProps: {
                  // display: 'line',
                  setValue: (target, value) => {
                    // 创建一个北四的过滤器并将id设置到北四
                    const filterId =  target.parent.getPropValue('filterId');
                    let runFn = creatFilter
                    const params = {
                      cardId: value,
                      pageId: getUrlParam('id') || '1' // 临时写死
                    }
                    if(filterId){
                      runFn = updateFilter;
                      params.id = filterId;
                    }
                    runFn(params).then((result: any) => {
                      const selectorType = _.get(result, 'filterMeta.content.selectorType')
                      const filterType = _.get(result, 'filterMeta.content.filterType');
                      target.parent.setPropValue('filterType', filterType);

                      target.parent.setPropValue('filterId', result?.id);

                      switch (selectorType) {
                        case "DS_INTERVAL":
                          target.parent.setPropValue('selectorType', 'interval');
                          break;

                        case "DS_ELEMENTS":
                          target.parent.setPropValue('selectorType', 'multipleSelect');
                          break;

                        case "TREE":
                          target.parent.setPropValue('selectorType', 'treeSelect');
                          break;

                        case "TEXT_MATCH":
                          target.parent.setPropValue('selectorType', 'textMatch');
                          break;

                        default: {
                          target.parent.setPropValue('selectorType', '');
                        }
                      }

                    })
                  }
                },
                setter: [
                  {
                    componentName: "CascaderSelectSetter",
                    props: {

                    }
                  }
                ]
              },
              {
                name: 'width',
                title: '宽度',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'filterId',
                title: '筛选器ID',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'selectorType',
                title: '数据类型',
                display: 'inline',
                // initialValue: 'time',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'singleTime', title: '时间单选' },
                      { value: 'select', title: '单选' },
                      { value: 'multipleSelect', title: '多选' },
                      { value: 'treeSelect', title: '树状选择' },
                      { value: 'interval', title: '范围' },
                      { value: 'textMatch', title: '条件' },
                    ],
                  },
                },
              },
              {
                name: 'multiple',
                title: '是否多选',
                display: 'inline',
                setter: 'BoolSetter',
                defaultValue: true,
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'treeSelect';
                },
              },
              // {
              //   name: 'hasPath',
              //   title: '是否带路径',
              //   display: 'inline',
              //   setter: 'BoolSetter',
              //   defaultValue: true,
              //   condition: (target) => {
              //     const selectorType = target.parent.getPropValue('selectorType');
              //     return selectorType === 'treeSelect';
              //   },
              // },
              {
                name: 'label',
                title: '名称',
                display: 'inline',
                setter: 'StringSetter'
              },
              {
                name: 'placeholder',
                title: '占位符',
                display: 'inline',
                setter: 'StringSetter',
              },
              // 其他默认值
              {
                name: 'defalutType',
                title: '默认方式',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'fixedValue', title: '固定值' },
                      { value: 'firstValue', title: '下拉列表中第一个' },
                    ],
                  },
                  initialValue: 'fixedValue',
                },
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType !== 'time';
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');
                    const defalutKey = target.parent.getPropValue('defalutKey');
                    const  currentFilter= {
                      key: map[selectorType],
                      filterId,
                      value: defalutKey,
                      defalutType
                    }
                    if(selectorType === 'treeSelect'){
                      currentFilter.pathFilterRegex = '@_@'
                    }
                    setGlobalTimeRange(currentFilter)
                  },
                },
              },
              {
                name: 'defalutKey',
                title: '默认值',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  const defalutType = target.parent.getPropValue('defalutType');

                  return selectorType && (!['time'].includes(selectorType)) && defalutType === 'fixedValue';
                  // return selectorType && (!['time'].includes(selectorType));
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');


                    const  currentFilter= {
                      key: map[selectorType],
                      filterId,
                      value,
                      defalutType
                    }
                    // 针对tree有带path的情况， 需要做数据切割， 分隔符设为@_@
                    if(selectorType === 'treeSelect'){
                      currentFilter.pathFilterRegex = '@_@'
                    }
                    setGlobalTimeRange(currentFilter)
                  },
                },
                setter: 'StringSetter'
              },
              {
                name: 'show',
                title: '是否显示',
                setter: 'BoolSetter',
                defaultValue: true,
                condition: (target) => {
                  return target.parent.getPropValue('selectorType') === 'singleTime';
                }
              },
              effectRanges,
            ],
          },
        },
        initialValue: () => {
          return {
            selectorType: '',
            width: 200,
          };
        },
      },
    },
  },
};

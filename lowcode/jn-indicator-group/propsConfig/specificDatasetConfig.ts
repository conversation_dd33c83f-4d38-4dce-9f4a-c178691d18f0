import numeral from 'numeral';
import { getIndicatorGroupData } from '../service/request';
import { generateCommonPayload } from '../../common/service/commonService';
import { cloneDeep, get } from 'lodash';
import { diffComparisonNew } from '../service/service';
import _ from 'lodash';
import { Message } from '@alifd/next';
import { requestPolarisPageConfig } from '../../common/service/request';


/**
 *  1. allDataColumn是做右侧渲染的字段， 做渲染用的
 *  2. allDataColumnClone 是做数据下拉选择的字段， 他是为了保存接口数据的缓冲
 *
 */
export const bindDatasetItems = [
  {
    name: 'indicatorGroupDataset.bindDataset',
    title: '数据绑定',
    display: 'inline',
    extraProps: {

      setValue: (target, value) => {
        if (value) {
          const requestChartPayload = generateCommonPayload(value);
          for (let i = 1; i <= 20; i++) {
            target.parent.setPropValue(`allDataColumn.${i}`, {
              dataType: 'text',
              dataSelect: '',
              showControl: '',
              mainField: '',
              mainTitle: '',
              originTitle: '',
              tooltipText: '',
              iconShowType: '',
              distLink: '',
              timeControl: false,
              timeRangeSelect: [],
              dataField: '',
              nickName: '',
            });
          }
          getIndicatorGroupData(requestChartPayload).then((res) => {
            const result = res?.data?.result;

            let index = 1;

            const allData = {}; // Setter 数据栏设置列表
            for (const item of result?.data?.headList) {
              target.parent.setPropValue(`allDataColumn.${index}.columns`, []);

              // 格式化数据
              const value = result?.data?.bodyList[0]?.[item.key];
              const valueOriginFormatType = result?.data?.formatMap[item.key]?.excelFormat;
              const valueFormatType = valueOriginFormatType?.replace('""', '');
              const formatValue = numeral(value).format(valueFormatType);

              // 指标卡数据列配置数据
              const formatChildItem = (
                item?.children?.length ? item?.children : item?.assistants
              ).map((childItem) => {
                const childValue = result?.data?.bodyList[0]?.[childItem.key];
                const childOriginFormatType = result?.data?.formatMap[childItem.key]?.excelFormat;
                const childValueFormatType = childOriginFormatType?.replace('""', '');

                return {
                  ...childItem,
                  dataType: 'text',
                  value: childValue,
                  dataSelect: childItem.key,
                  dataField: childItem.key,
                  nickName: childItem.title,
                  formatType: childValueFormatType,
                  showControl: true,
                };
              });

              // 指标卡总配置数据
              const itemData = {
                ...item,
                dataType: 'text',
                value: formatValue,
                dataField: item.key,
                nickName: item.title,
                formatType: valueFormatType,
                children: formatChildItem,
                showControl: true,
                YAxisShowType: result?.data?.headList?.slice(1)?.length > 1 ? 'double' : 'single',
              };
              // 获取所有的字段
              const newItem = {
                columns: itemData?.children,
                mainField: item?.key,
                key: item.key,
                originTitle: item?.title,
                mainTitle: item?.title,
                tooltipText: '',
                iconShowType: '',
                distLink: '',
                timeControl: false,
                timeRangeSelect: [],
              };
              allData[`${index}`] = _.cloneDeep(newItem);
              target.parent.setPropValue(`allDataColumn.${index}`, newItem);
              index += 1;
            }

            target.parent.setPropValue('allDataColumnClone', allData);
          });

          return value;
        }
      },
    },
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        showSearch: true,
        filterType: 'ObjectList',
      },
    },
  },
  {
    name: 'indicatorGroupDataset.syncData',
    title: '同步数据',
    display: 'inline',
    extraProps: {
      setValue: (target) => {
        // 获取组件的id、原数据列、原指标组数据源
        const bindDataset = target.getProps().getPropValue('indicatorGroupDataset.bindDataset');
        const allDataColumn = target.parent.getPropValue('allDataColumn'); // 编辑的数据列


        // 获取绑定数据集的数据的筛选条件
        const requestChartPayload = generateCommonPayload(bindDataset);

        // 先进行数据清空
        for (let i = 1; i <= 20; i++) {
          target.parent.setPropValue(`allDataColumn.${i}`, {
            dataType: 'text',
            dataSelect: '',
            showControl: '',
            mainField: '',
            mainTitle: '',
            originTitle: '',
            tooltipText: '',
            distLink: '',
            timeControl: false,
            timeRangeSelect: [],
            dataField: '',
            nickName: '',
          });
        }

        // 获取绑定数据集的数据
        getIndicatorGroupData(requestChartPayload).then((res) => {
          if (res.data.code !== 'ok') {
            Message.error('同步失败');
            return;
          }
          // 同步时间
          target.parent.setPropValue(fetch, `fetch${Date.now()}`);
          const result = res?.data?.result;

          // 接口返回数据column
          const allDataColumnCopy = Object.values(cloneDeep(allDataColumn));
          const apiResultHeadListForAllDataColumn = cloneDeep(result?.data?.headList);

          const nextDataColumn = [];

          // 获取数据列
          const filterEmptyColumn = allDataColumnCopy.filter((item) => item.mainField || item.key);


          // console.log('--------diff---------', allDataColumnCopy, apiResultHeadListForAllDataColumn)

          // 对数据进行对比
          diffComparisonNew(
            filterEmptyColumn,
            apiResultHeadListForAllDataColumn,
            nextDataColumn,
          );

          // 进行数据重新赋值
          const allData = {}; // Setter 数据栏设置列表
          for (let i = 1; i <= 20; i++) {

            const item = nextDataColumn[i - 1];
            if (item) {
              // newAllData[`${i}`] = item;
              allData[`${i}`] = _.cloneDeep(item);
              target.parent.setPropValue(`allDataColumn.${i}`, _.cloneDeep(item));
            } else {
              // newAllData[`${i}`] = {}
              allData[`${i}`] = {};
              target.parent.setPropValue(`allDataColumn.${i}`, {
                dataType: 'text',
                dataSelect: '',
                showControl: '',
                mainField: '',
                mainTitle: '',
                originTitle: '',
                tooltipText: '',
                distLink: '',
                timeControl: false,
                timeRangeSelect: [],
                dataField: '',
                nickName: '',
              });
            }
          }

          target.parent.setPropValue(`allDataColumnClone`, allData);
          Message.success('同步成功！');
        });
      },
    },
    setter: [
      {
        componentName: 'ButtonSetter',
        props: {
          title: '同步数据',
        },
      },
    ],
  },
];

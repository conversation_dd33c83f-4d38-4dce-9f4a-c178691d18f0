export const CHART_DATA = [
  // 销售金额
  {
    name: '销售金额_趋势图',
    filters: [
      {
        name: '统计日期',
        fdId: 's4421e1c6744743f3aab7453',
        dsId: 'vc6e963168a114f558c506f0',
        cdId: 't776ec88817324dc7b4f9773',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [
          {
            name: '统计日期',
            displayName: '统计日期',
            parentFdName: null,
            fdId: 's4421e1c6744743f3aab7453',
            fdType: 'DATE',
            granularity: null,
            format: null,
          },
        ],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '销售金额',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同比',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        axes: {
          categoryAxis: {
            labelFontSize: 11,
            titleFontSize: 12,
            titleColor: 'default',
            step: '',
            labelColor: 'default',
            visible: true,
            distance: '',
            title: null,
            autoStep: true,
            showTitle: false,
          },
          mainAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          secondaryAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          navigator: {
            showNavigator: false,
            fixedValueAxis: true,
          },
        },
        dynamicParameters: [],
        chartLegend: {
          invisibleLegendKeys: [],
          legendPosition: 'top',
        },
        themeColor: {
          theme: 'CUSTOM',
          tcId: 'sc7ad79dd564e442e9cb8021',
          colors: [null, null, null, null, null, null, null, null, null, null, null],
        },
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dataLabels: {
          metric: {
            showSeries: false,
            showCategory: false,
            showNumber: true,
            fontSize: 12,
            separator: 'comma',
            position: 'outside',
          },
        },
        auxiliaryLine: null,
        theme: 'CUSTOM',
        colors: [
          '#489cff',
          '#8bd12e',
          '#FB8437',
          '#FFC300',
          '#2CCA78',
          '#4DB6FE',
          '#B8CCFF',
          '#997DED',
          '#DD74D8',
          '#1d2129',
          '#86909c',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      categories: [
        '2023-06-04',
        '2023-06-05',
        '2023-06-06',
        '2023-06-07',
        '2023-06-08',
        '2023-06-09',
        '2023-06-10',
      ],
      series: [
        {
          name: '销售金额',
          key: 'IdrrViORud',
          data: [
            {
              y: 16713176.2775,
              colorBy: null,
            },
            {
              y: 16826331.0785,
              colorBy: null,
            },
            {
              y: 15531556.0825,
              colorBy: null,
            },
            {
              y: 15263672.4375,
              colorBy: null,
            },
            {
              y: 18895891.9025,
              colorBy: null,
            },
            {
              y: 18970854.6199,
              colorBy: null,
            },
            {
              y: 17026237.055,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          yAxis: 0,
          metric_additional: false,
        },
        {
          name: '同比',
          key: 'EZgzfGJqdx',
          data: [
            {
              y: 0.864931,
              colorBy: null,
            },
            {
              y: 0.792325,
              colorBy: null,
            },
            {
              y: 0.842688,
              colorBy: null,
            },
            {
              y: 0.272963,
              colorBy: null,
            },
            {
              y: 0.747466,
              colorBy: null,
            },
            {
              y: 1.014766,
              colorBy: null,
            },
            {
              y: 1.682168,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          yAxis: 1,
          metric_additional: true,
        },
      ],
      yAxis: [
        {
          title: {
            text: 'mainAxis',
          },
        },
        {
          title: {
            text: 'secondaryAxis',
          },
          opposite: true,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'MULTI_LINE',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '68c3f0c5294068f7',
      cdId: 't776ec88817324dc7b4f9773',
      cdName: '销售金额_趋势图',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315251,
        endTime: 1686452315253,
        duration: 2,
      },
      askTime: {
        startTime: 1686452315236,
        endTime: 1686452315253,
        duration: 17,
      },
      otherTime: 15,
    },
  },
  // 付款毛利率
  {
    name: '付款毛利率_趋势图',
    filters: [
      {
        name: '时间',
        fdId: 'wc9606ce6815540189981973',
        dsId: 'c3f022dd060ce4af7afc20f7',
        cdId: 'o9242a4169f6f4ae591968c4',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [
      {
        customize: false,
        dpId: 'sb40ab1b2143742078dde776',
        valueType: 'STRING',
        name: '日月',
        multiple: false,
        defaultValue: '日',
        freeze: false,
        fdId: 'k48378db5bf944ad999556e3',
        optionValue: ['日', '月'],
        description: '',
        domId: 'guanbi',
      },
    ],
    chartMain: {
      meta: {
        categories: [
          {
            name: '时间',
            displayName: '时间',
            parentFdName: null,
            fdId: 'wc9606ce6815540189981973',
            fdType: 'DATE',
            granularity: null,
            format: null,
          },
        ],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '毛利率',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '目标',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        axes: {
          categoryAxis: {
            labelFontSize: 11,
            titleFontSize: 12,
            titleColor: 'default',
            step: '',
            labelColor: 'default',
            visible: true,
            distance: '',
            title: null,
            autoStep: true,
            showTitle: false,
          },
          mainAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          secondaryAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          navigator: {
            showNavigator: false,
            fixedValueAxis: true,
          },
        },
        dynamicParameters: [
          {
            customize: false,
            dpId: 'sb40ab1b2143742078dde776',
            valueType: 'STRING',
            name: '日月',
            multiple: false,
            defaultValue: '日',
            freeze: false,
            fdId: 'k48378db5bf944ad999556e3',
            optionValue: ['日', '月'],
            description: '',
            domId: 'guanbi',
          },
          {
            customize: false,
            dpId: 'k6ae21ed7b28740b6841cad5',
            valueType: 'DATE',
            name: '结束日期(MTD)',
            multiple: false,
            defaultValue: '{{{yesterday}}}',
            freeze: false,
            fdId: 'b3cde1ca2399a451cb2d8138',
            optionValue: [''],
            description: '',
            domId: 'guanbi',
          },
          {
            customize: false,
            dpId: 'o7ea0a0a00cbe4cfbaeb2d15',
            valueType: 'DATE',
            name: '开始日期（MTD）',
            multiple: false,
            defaultValue: '{{{first day of this month}}}',
            freeze: false,
            fdId: 'u69a1f14e723e4920b23f9db',
            optionValue: [''],
            description: 'mtd',
            domId: 'guanbi',
          },
          {
            customize: false,
            dpId: 'k6ae21ed7b28740b6841cad5',
            valueType: 'DATE',
            name: '结束日期(MTD)',
            multiple: false,
            defaultValue: '{{{yesterday}}}',
            freeze: false,
            fdId: 'fba4458e78f0d4c05b5359c2',
            optionValue: [''],
            description: '',
            domId: 'guanbi',
          },
        ],
        chartLegend: {
          color: null,
          bold: false,
          showLegend: true,
          underline: false,
          invisibleLegendKeys: [],
          fontSize: 12,
          italic: false,
          fontFamily: 'sans-serif',
          legendPosition: 'top',
        },
        themeColor: {
          theme: 'CUSTOM',
          tcId: 'sc7ad79dd564e442e9cb8021',
          colors: [null, null, null, null, null, null, null, null, null, null, null],
        },
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        auxiliaryLine: null,
        theme: 'CUSTOM',
        colors: [
          '#489cff',
          '#8bd12e',
          '#FB8437',
          '#FFC300',
          '#2CCA78',
          '#4DB6FE',
          '#B8CCFF',
          '#997DED',
          '#DD74D8',
          '#1d2129',
          '#86909c',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      categories: [
        '2023-06-04',
        '2023-06-05',
        '2023-06-06',
        '2023-06-07',
        '2023-06-08',
        '2023-06-09',
        '2023-06-10',
      ],
      series: [
        {
          name: '毛利率',
          key: 'hkEXTIRtmi',
          data: [
            {
              y: 0.5865,
              colorBy: null,
            },
            {
              y: 0.569131,
              colorBy: null,
            },
            {
              y: 0.583499,
              colorBy: null,
            },
            {
              y: 0.581982,
              colorBy: null,
            },
            {
              y: 0.572447,
              colorBy: null,
            },
            {
              y: 0.571735,
              colorBy: null,
            },
            {
              y: 0.589594,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          yAxis: 0,
          metric_additional: false,
        },
        {
          name: '同期',
          key: 'wdxDEimwJn',
          data: [
            {
              y: 0.596963,
              colorBy: null,
            },
            {
              y: 0.580012,
              colorBy: null,
            },
            {
              y: 0.593691,
              colorBy: null,
            },
            {
              y: 0.593529,
              colorBy: null,
            },
            {
              y: 0.584679,
              colorBy: null,
            },
            {
              y: 0.58384,
              colorBy: null,
            },
            {
              y: 0.599753,
              colorBy: null,
            },
          ],
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          yAxis: 0,
          metric_additional: false,
        },
        {
          name: '目标',
          key: 'AjhRzYGsKn',
          data: [
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
            {
              y: 0.581049,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          yAxis: 0,
          metric_additional: false,
        },
      ],
      yAxis: [
        {
          title: {
            text: 'mainAxis',
          },
        },
        {
          title: {
            text: 'secondaryAxis',
          },
          opposite: true,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'MULTI_LINE',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '34428174f85ae8f0',
      cdId: 'o9242a4169f6f4ae591968c4',
      cdName: '付款毛利率_趋势图',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315313,
        endTime: 1686452315315,
        duration: 2,
      },
      askTime: {
        startTime: 1686452315259,
        endTime: 1686452315315,
        duration: 56,
      },
      otherTime: 54,
    },
  },
  // 30天可用成本周转
  {
    name: '30天可用成本周转_趋势',
    filters: [
      {
        name: '时间整合',
        fdId: 'ubce0d7738b59447bb653a06',
        dsId: 'u7502c3f95bc54e39b15cd82',
        cdId: 'o315ed866570146d3ad24790',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [
          {
            name: '时间整合',
            displayName: '时间整合',
            parentFdName: null,
            fdId: 'ubce0d7738b59447bb653a06',
            fdType: 'DATE',
            granularity: null,
            format: null,
          },
        ],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '30天可用周转',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        axes: {
          categoryAxis: {
            labelFontSize: 11,
            titleFontSize: 12,
            titleColor: 'default',
            step: '',
            labelColor: 'default',
            visible: true,
            distance: '',
            title: null,
            autoStep: true,
            showTitle: false,
          },
          mainAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          secondaryAxis: {
            tickInterval: null,
            labelFontSize: 11,
            max: '',
            unit: null,
            tickPosition: null,
            titleFontSize: 12,
            titleColor: 'default',
            labelColor: 'default',
            autoTickInterval: true,
            visible: true,
            min: '',
            distance: '',
            autoExtremes: true,
            showGridLine: true,
            title: null,
            showTitle: false,
            minorTickPosition: null,
          },
          navigator: {
            showNavigator: false,
            fixedValueAxis: true,
          },
        },
        dynamicParameters: [
          {
            customize: false,
            dpId: 'o7ea0a0a00cbe4cfbaeb2d15',
            valueType: 'DATE',
            name: '开始日期（MTD）',
            multiple: false,
            defaultValue: '{{{first day of this month}}}',
            freeze: false,
            fdId: 'kd0e18fc6d6bf4b339fcf366',
            optionValue: [''],
            description: 'mtd',
            domId: 'guanbi',
          },
        ],
        chartLegend: {
          invisibleLegendKeys: [],
        },
        themeColor: {
          theme: 'CUSTOM',
          tcId: 'sc7ad79dd564e442e9cb8021',
          colors: [null, null, null, null, null, null, null, null, null, null, null],
        },
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        auxiliaryLine: null,
        theme: 'CUSTOM',
        colors: [
          '#489cff',
          '#8bd12e',
          '#FB8437',
          '#FFC300',
          '#2CCA78',
          '#4DB6FE',
          '#B8CCFF',
          '#997DED',
          '#DD74D8',
          '#1d2129',
          '#86909c',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      categories: [
        '2023-06-04',
        '2023-06-05',
        '2023-06-06',
        '2023-06-07',
        '2023-06-08',
        '2023-06-09',
        '2023-06-10',
      ],
      series: [
        {
          name: '30天可用周转',
          key: 'hnZWlYWpyO',
          data: [
            {
              y: 76.834309,
              colorBy: null,
            },
            {
              y: 76.386539,
              colorBy: null,
            },
            {
              y: 75.685203,
              colorBy: null,
            },
            {
              y: 75.407823,
              colorBy: null,
            },
            {
              y: 76.070923,
              colorBy: null,
            },
            {
              y: 76.304186,
              colorBy: null,
            },
            {
              y: 75.62083,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          yAxis: 0,
          metric_additional: false,
        },
        {
          name: '同期',
          key: 'LOMObQyZZr',
          data: [
            {
              y: 67.21864,
              colorBy: null,
            },
            {
              y: 67.340738,
              colorBy: null,
            },
            {
              y: 66.539627,
              colorBy: null,
            },
            {
              y: 65.688734,
              colorBy: null,
            },
            {
              y: 64.993374,
              colorBy: null,
            },
            {
              y: 65.119389,
              colorBy: null,
            },
            {
              y: 65.344677,
              colorBy: null,
            },
          ],
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          yAxis: 1,
          metric_additional: true,
        },
      ],
      yAxis: [
        {
          title: {
            text: 'mainAxis',
          },
        },
        {
          title: {
            text: 'secondaryAxis',
          },
          opposite: true,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'MULTI_LINE',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '0b7c517c9e1d4056',
      cdId: 'o315ed866570146d3ad24790',
      cdName: '30天可用成本周转_趋势',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315300,
        endTime: 1686452315302,
        duration: 2,
      },
      askTime: {
        startTime: 1686452315243,
        endTime: 1686452315302,
        duration: 59,
      },
      otherTime: 57,
    },
  },
];

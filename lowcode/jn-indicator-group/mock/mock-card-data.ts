import { ICardListRequest } from '../service/request';

export const CARD_DATA = [
  // 销售金额 + 达成率
  {
    name: '销售金额',
    filters: [
      {
        name: '统计日期',
        fdId: 's4421e1c6744743f3aab7453',
        dsId: 'vc6e963168a114f558c506f0',
        cdId: 'tc5c0dbcef35b40739979149',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '销售金额',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '目标',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '达成率',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同比',
              fdType: 'DOUBLE',
              idx: 3,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '环比',
              fdType: 'DOUBLE',
              idx: 4,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'dcf205df241604b3a96ed4ae',
            size: 20,
            bold: false,
            color: '#1d2129',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'j8034dca3818b45fa97c46ec',
            bold: false,
            color: '#4e5969',
          },
          label: {
            color: '#86909c',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        dynamicParameters: [],
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '销售金额',
          key: 'IdrrViORud',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 119227719.4534,
          threshold: null,
          t_idx: null,
        },
        {
          name: '目标',
          key: 'ZlwDdvJAgL',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 107899830.5739,
          threshold: null,
          t_idx: null,
        },
        {
          name: '达成率',
          key: 'fxJNeunqYI',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 1.104985,
          threshold: [
            {
              replaceText: null,
              font: {
                color: '#59c252',
              },
              background: null,
              condition: [
                {
                  type: 'condition',
                  not: false,
                  value: {
                    fdId: 'x5a5c60bc98374ad5be7c61d',
                    name: '目标达成率',
                    alias: '达成率',
                    fdType: 'DOUBLE',
                    metaType: 'METRIC',
                    formula: 'sum([GMV])/sum([GMV目标])',
                    isAggregated: true,
                    calculationType: 'aggregation',
                    filterType: 'GT',
                    filterValue: ['1'],
                    key: 'fxJNeunqYI',
                  },
                },
              ],
            },
          ],
          t_idx: 0,
        },
        {
          name: '同比',
          key: 'HrHxBROIXu',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 0.82455,
          threshold: [],
          t_idx: null,
        },
        {
          name: '环比',
          key: 'xKsRhgolZU',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: -0.543334,
          threshold: [],
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: 'c975903b05f1d56e',
      cdId: 'tc5c0dbcef35b40739979149',
      cdName: '销售金额',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315366,
        endTime: 1686452315367,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315349,
        endTime: 1686452315367,
        duration: 18,
      },
      otherTime: 17,
    },
    addition: {
      limit: 1000,
      name: '目标达成率',
      offset: 0,
      rowExpand: null,
      view: 'GRAPH',
      filters: [
        {
          name: '日期',
          fdId: 'h8f5161feb2b04e46a95babd',
          dsId: 'q6b89b5fea8bb434faade71c',
          cdId: 'da072e3b666ca45fd99d6a9c',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      chartMain: {
        meta: {
          categories: [],
          series: [
            {
              cols: [
                {
                  value: null,
                },
              ],
              metric: {
                name: '目标达成率',
                fdType: 'DOUBLE',
                idx: 0,
              },
            },
          ],
          direction: 'vertical',
          splitSetting: null,
          mainKpiSetting: {
            label: {
              show: false,
              size: 14,
              color: '#202122',
              bold: true,
              family: 'q63dc8b2178054e5db0ecd2b',
            },
            value: {
              family: 'dcf205df241604b3a96ed4ae',
              size: 24,
              bold: false,
              color: '#343d50',
            },
          },
          secondKpiSetting: {
            iconStyle: 'positiveRed',
            layout: 'classic',
            iconShowMap: {
              0: false,
              1: false,
              2: true,
              3: true,
            },
            value: {
              size: 12,
              family: 'j8034dca3818b45fa97c46ec',
              bold: false,
              color: '#617282',
            },
            label: {
              color: '#617282',
              size: 12,
              family: 'q63dc8b2178054e5db0ecd2b',
              bold: false,
            },
            alignment: 'left',
            dividingLineColor: 'rgba(247, 247, 247, 1)',
          },
          tooltip: {
            enabled: false,
            maxNumber: 20,
            order: 'value',
            onlyTooltipSeries: false,
          },
          dynamicParameters: [],
          auxiliaryLine: null,
          axes: {
            mainAxis: {},
            secondaryAxis: {},
            baseAxis: {},
          },
          theme: 'DEFAULT_COLOR',
          colors: [
            '#0781C3',
            '#FF7500',
            '#83BFF4',
            '#FFB86C',
            '#3FB27E',
            '#9BCC7E',
            '#F35352',
            '#FF9193',
            '#9D61C2',
            '#CAAED8',
            '#8FA500',
            '#BFD000',
          ],
        },
        colorAxis: null,
        limitInfo: {
          hasMoreData: false,
          dataLimit: 5000,
          hasMoreCol: false,
          colLimit: 1000,
        },
        series: [
          {
            name: '目标达成率',
            key: 'PuSlIJWulK',
            format: {
              specifier: '.1%',
              suffix: '',
              divideDataBy: 1,
              isAuto: false,
              decimalPlaces: 1,
              excelFormat: '0.0%""',
            },
            value: 1.107435,
            threshold: null,
            t_idx: null,
          },
        ],
      },
      rawDataNotChanged: false,
      chartType: 'KPI_CARD',
      cardType: 'CHART',
      performanceAnalysis: {
        traceId: 'a4a056bcc93e4b3a',
        cdId: 'da072e3b666ca45fd99d6a9c',
        cdName: '目标达成率',
        hitCache: 1,
        dataParse: {
          startTime: 1686452315487,
          endTime: 1686452315488,
          duration: 1,
        },
        askTime: {
          startTime: 1686452315463,
          endTime: 1686452315488,
          duration: 25,
        },
        otherTime: 24,
      },
    },
  },
  // 付款毛利率
  {
    name: '付款毛利率',
    filters: [
      {
        name: '时间',
        fdId: 'wc9606ce6815540189981973',
        dsId: 'c3f022dd060ce4af7afc20f7',
        cdId: 'x37b63326f41141c99c6621b',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '毛利率',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '目标',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [
          {
            customize: false,
            dpId: 'k6ae21ed7b28740b6841cad5',
            valueType: 'DATE',
            name: '结束日期(MTD)',
            multiple: false,
            defaultValue: '{{{yesterday}}}',
            freeze: false,
            fdId: 'b3cde1ca2399a451cb2d8138',
            optionValue: [''],
            description: '',
            domId: 'guanbi',
          },
          {
            customize: false,
            dpId: 'o7ea0a0a00cbe4cfbaeb2d15',
            valueType: 'DATE',
            name: '开始日期（MTD）',
            multiple: false,
            defaultValue: '{{{first day of this month}}}',
            freeze: false,
            fdId: 'u69a1f14e723e4920b23f9db',
            optionValue: [''],
            description: 'mtd',
            domId: 'guanbi',
          },
          {
            customize: false,
            dpId: 'k6ae21ed7b28740b6841cad5',
            valueType: 'DATE',
            name: '结束日期(MTD)',
            multiple: false,
            defaultValue: '{{{yesterday}}}',
            freeze: false,
            fdId: 'fba4458e78f0d4c05b5359c2',
            optionValue: [''],
            description: '',
            domId: 'guanbi',
          },
        ],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '付款毛利率',
          key: 'hkEXTIRtmi',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 0.578971,
          threshold: null,
          t_idx: null,
        },
        {
          name: '毛利率',
          key: 'hkEXTIRtmi',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 0.578971,
          threshold: null,
          t_idx: null,
        },
        {
          name: '目标',
          key: 'bBjjMPXwBB',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 0.581049,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同期',
          key: 'CCHCYsZsNd',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.57681,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '6c968affd113a325',
      cdId: 'x37b63326f41141c99c6621b',
      cdName: '付款毛利率',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315454,
        endTime: 1686452315455,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315398,
        endTime: 1686452315455,
        duration: 57,
      },
      otherTime: 56,
    },
  },
  // 初上市价折扣率
  {
    name: '初上市价折扣率',
    filters: [
      {
        name: '时间',
        fdId: 'wc9606ce6815540189981973',
        dsId: 'c3f022dd060ce4af7afc20f7',
        cdId: 'c61543cfd52214d5d9a3887a',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '初上市价折扣率',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '初上市价折扣率',
          key: 'OQCCQJllcE',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.777411,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同期',
          key: 'LmkxoFQftb',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.851767,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: 'b6b505450aefd505',
      cdId: 'c61543cfd52214d5d9a3887a',
      cdName: '初上市价折扣率',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315427,
        endTime: 1686452315427,
        duration: 0,
      },
      askTime: {
        startTime: 1686452315373,
        endTime: 1686452315427,
        duration: 54,
      },
      otherTime: 54,
    },
  },
  // 客单价
  {
    name: '客单价',
    filters: [
      {
        name: '日期',
        fdId: 'x7c5a9f7f07694730814c02a',
        dsId: 'jdd0fa49a113740dd8296325',
        cdId: 'ceb4f661e1ee24d549dd8240',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '客单价',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同比',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '环比',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'dcf205df241604b3a96ed4ae',
            size: 24,
            bold: false,
            color: '#343d50',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: true,
            1: true,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'j8034dca3818b45fa97c46ec',
            bold: false,
            color: '#617282',
          },
          label: {
            color: '#617282',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '客单价',
          key: 'WtvZYHpayy',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 177.763844,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同比',
          key: 'WXhBefVmLA',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: -0.010801,
          threshold: null,
          t_idx: null,
        },
        {
          name: '环比',
          key: 'vXFHDHbRDA',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: -0.007094,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '7ca1e4ad4accdb59',
      cdId: 'ceb4f661e1ee24d549dd8240',
      cdName: '客单价',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315438,
        endTime: 1686452315439,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315414,
        endTime: 1686452315439,
        duration: 25,
      },
      otherTime: 24,
    },
  },
  // 年累计销售+年目标进度
  {
    name: '年累计销售金额',
    filters: [],
    dynamicParams: [],
    addition: {
      name: '年目标进度',
      filters: [],
      dynamicParams: [],
      chartMain: {
        colorBy: null,
        value: 0.404365,
        meta: {
          progressBarSetting: {
            progress: '#76ADEE',
            background: 'rgba(241, 242, 244, 1)',
            color: '#343d50',
            intervalColors: [
              {
                key: 'hcLFAw',
                color: 'rgba(72, 156, 255, 1)',
                interval: [0, 100],
                type: 'interval',
              },
            ],
          },
          dynamicParameters: [],
          auxiliaryLine: null,
          axes: {
            mainAxis: {},
            secondaryAxis: {},
            baseAxis: {},
          },
          theme: 'DEFAULT_COLOR',
          colors: [
            '#0781C3',
            '#FF7500',
            '#83BFF4',
            '#FFB86C',
            '#3FB27E',
            '#9BCC7E',
            '#F35352',
            '#FF9193',
            '#9D61C2',
            '#CAAED8',
            '#8FA500',
            '#BFD000',
          ],
        },
        series: [
          {
            value: 1905844659.052901,
            format: null,
            name: '线上支付线下实销',
          },
          {
            value: 4713181013,
            format: null,
            name: '全年目标',
          },
        ],
      },
      view: 'GRAPH',
      rawDataNotChanged: false,
      chartType: 'PROGRESS_BAR',
      cardType: 'CHART',
      performanceAnalysis: {
        traceId: 'd2d3bc3a75ae63e0',
        cdId: 'le12bca4090c442ac974eb94',
        cdName: '年目标进度',
        hitCache: 1,
        dataParse: {
          startTime: 1686452315312,
          endTime: 1686452315312,
          duration: 0,
        },
        askTime: {
          startTime: 1686452315259,
          endTime: 1686452315312,
          duration: 53,
        },
        otherTime: 53,
      },
    },
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '年累计销售金额',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '全年目标',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同比',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '剩余',
              fdType: 'DOUBLE',
              idx: 3,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'style2',
          iconShowMap: {
            0: false,
            1: true,
            2: false,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        dynamicParameters: [],
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '年累计销售金额',
          key: 'IdrrViORud',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 1899511546.473499,
          threshold: null,
          t_idx: null,
        },
        {
          name: '全年目标',
          key: 'duEvrZbOIr',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 4798143279,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同比',
          key: 'HrHxBROIXu',
          format: {
            specifier: ',.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '#,###0.0%""',
          },
          value: 0.696021,
          threshold: [],
          t_idx: null,
        },
        {
          name: '剩余',
          key: 'sIWmcDguEa',
          format: {
            suffix: '天',
            isAuto: false,
            excelFormat: '0"天"',
          },
          value: 204,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '07453512a1d22a39',
      cdId: 'da508482cd8c64a1bb86f9dd',
      cdName: '年累计销售金额',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315331,
        endTime: 1686452315332,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315312,
        endTime: 1686452315332,
        duration: 20,
      },
      otherTime: 19,
    },
  },
  // 退款率(追溯)
  {
    name: '退款率(追溯)',
    filters: [
      {
        name: '时间',
        fdId: 'wc9606ce6815540189981973',
        dsId: 'c3f022dd060ce4af7afc20f7',
        cdId: 'a6049d95ece06456ba448b05',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '退款率(追溯)',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '退款率(追溯)',
          key: 'ewhQklRYWw',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.153681,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同期',
          key: 'qMfUnyoFAg',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.214038,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '1115febe2cb2983f',
      cdId: 'a6049d95ece06456ba448b05',
      cdName: '退款率(追溯)',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315413,
        endTime: 1686452315413,
        duration: 0,
      },
      askTime: {
        startTime: 1686452315359,
        endTime: 1686452315413,
        duration: 54,
      },
      otherTime: 54,
    },
  },
  // 30天可用成本周转
  {
    name: '30天可用成本周转',
    filters: [
      {
        name: '时间整合',
        fdId: 'ubce0d7738b59447bb653a06',
        dsId: 'u7502c3f95bc54e39b15cd82',
        cdId: 'nfee9db5a72554d12ac3e6c0',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '30天可用周转',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '同期',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: false,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [
          {
            customize: false,
            dpId: 'o7ea0a0a00cbe4cfbaeb2d15',
            valueType: 'DATE',
            name: '开始日期（MTD）',
            multiple: false,
            defaultValue: '{{{first day of this month}}}',
            freeze: false,
            fdId: 'kd0e18fc6d6bf4b339fcf366',
            optionValue: [''],
            description: 'mtd',
            domId: 'guanbi',
          },
        ],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '30天可用成本周转',
          key: 'epbUjXGuhw',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 75.62083,
          threshold: null,
          t_idx: null,
        },
        {
          name: '30天可用周转',
          key: 'epbUjXGuhw',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 75.62083,
          threshold: null,
          t_idx: null,
        },
        {
          name: '同期',
          key: 'VpuWtEeLku',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 65.344677,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: 'd81bc920f73a59fd',
      cdId: 'nfee9db5a72554d12ac3e6c0',
      cdName: '30天可用成本周转',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315392,
        endTime: 1686452315393,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315346,
        endTime: 1686452315393,
        duration: 47,
      },
      otherTime: 46,
    },
  },
  // 会员GMV贡献率
  {
    name: '会员GMV贡献率',
    filters: [
      {
        name: '日期',
        fdId: 'x7c5a9f7f07694730814c02a',
        dsId: 'jdd0fa49a113740dd8296325',
        cdId: 'q05e43943b98b4d299e8e96a',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '会员贡献率',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '会员GMV贡献率',
          key: 'sEivtFGQXE',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.375987,
          threshold: null,
          t_idx: null,
        },
        {
          name: '会员贡献率',
          key: 'sEivtFGQXE',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.375987,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '7b21ee137ed21510',
      cdId: 'q05e43943b98b4d299e8e96a',
      cdName: '会员GMV贡献率',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315471,
        endTime: 1686452315472,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315436,
        endTime: 1686452315472,
        duration: 36,
      },
      otherTime: 35,
    },
  },
  // 差评率
  {
    name: '差评率',
    filters: [
      {
        name: '统计日期',
        fdId: 'd5e892ac78a684e38bd49593',
        dsId: 'vb877478cd2c14cb3998476f',
        cdId: 'r3697cda45b69476eb05ee3f',
        fdType: 'DATE',
        filterType: 'BT',
        filterValue: ['2023-06-04', '2023-06-10'],
      },
    ],
    dynamicParams: [],
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '差评率',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: true,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: false,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '差评率',
          key: 'gpjRExSBkq',
          format: {
            specifier: '.1%',
            suffix: '',
            divideDataBy: 1,
            isAuto: false,
            decimalPlaces: 1,
            excelFormat: '0.0%""',
          },
          value: 0.069995,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: 'b4ae50d9ebb2eb07',
      cdId: 'r3697cda45b69476eb05ee3f',
      cdName: '差评率',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315466,
        endTime: 1686452315467,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315442,
        endTime: 1686452315467,
        duration: 25,
      },
      otherTime: 24,
    },
  },
  // 月累计销售+月目标进度
  {
    name: '月累计销售金额',
    filters: [],
    dynamicParams: [],
    addition: {
      name: '月目标进度',
      filters: [],
      dynamicParams: [],
      chartMain: {
        colorBy: null,
        value: 0.436366,
        meta: {
          dynamicParameters: [
            {
              customize: false,
              dpId: 'o7ea0a0a00cbe4cfbaeb2d15',
              valueType: 'DATE',
              name: '开始日期（MTD）',
              multiple: false,
              defaultValue: '{{{first day of this month}}}',
              freeze: false,
              fdId: 'i304dc81dc01d4be58528ede',
              optionValue: [''],
              description: 'mtd',
              domId: 'guanbi',
            },
          ],
          progressBarSetting: {
            progress: '#76ADEE',
            background: 'rgba(241, 242, 244, 1)',
            color: '#343d50',
            intervalColors: [
              {
                key: 'hcLFAw',
                color: 'rgba(72, 156, 255, 1)',
                interval: [0, 100],
                type: 'interval',
              },
            ],
          },
          auxiliaryLine: null,
          axes: {
            mainAxis: {},
            secondaryAxis: {},
            baseAxis: {},
          },
          theme: 'DEFAULT_COLOR',
          colors: [
            '#0781C3',
            '#FF7500',
            '#83BFF4',
            '#FFB86C',
            '#3FB27E',
            '#9BCC7E',
            '#F35352',
            '#FF9193',
            '#9D61C2',
            '#CAAED8',
            '#8FA500',
            '#BFD000',
          ],
        },
        series: [
          {
            value: 244164545.3535,
            format: null,
            name: '本月累计gmv',
          },
          {
            value: 559541246.9992,
            format: null,
            name: '本月目标',
          },
        ],
      },
      view: 'GRAPH',
      rawDataNotChanged: false,
      chartType: 'PROGRESS_BAR',
      cardType: 'CHART',
      performanceAnalysis: {
        traceId: '153b14f262e150f2',
        cdId: 'a5702e94831244cccac4d42f',
        cdName: '月目标进度',
        hitCache: 1,
        dataParse: {
          startTime: 1686452315290,
          endTime: 1686452315290,
          duration: 0,
        },
        askTime: {
          startTime: 1686452315264,
          endTime: 1686452315290,
          duration: 26,
        },
        otherTime: 26,
      },
    },
    chartMain: {
      meta: {
        categories: [],
        series: [
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '月累计销售金额',
              fdType: 'DOUBLE',
              idx: 0,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '本月目标',
              fdType: 'DOUBLE',
              idx: 1,
            },
          },
          {
            cols: [
              {
                value: null,
              },
            ],
            metric: {
              name: '剩余',
              fdType: 'DOUBLE',
              idx: 2,
            },
          },
        ],
        direction: 'vertical',
        splitSetting: null,
        mainKpiSetting: {
          label: {
            show: false,
            size: 14,
            color: '#202122',
            bold: true,
            family: 'q63dc8b2178054e5db0ecd2b',
          },
          value: {
            family: 'p3f2e18cab4ce4003a6fd55e',
            size: 24,
            bold: false,
            color: '#202122',
          },
        },
        secondKpiSetting: {
          iconStyle: 'positiveRed',
          layout: 'classic',
          iconShowMap: {
            0: false,
            1: false,
            2: false,
            3: true,
          },
          value: {
            size: 12,
            family: 'p3f2e18cab4ce4003a6fd55e',
            bold: false,
            color: '#606266',
          },
          label: {
            color: '#909399',
            size: 12,
            family: 'q63dc8b2178054e5db0ecd2b',
            bold: false,
          },
          alignment: 'left',
          dividingLineColor: 'rgba(247, 247, 247, 1)',
        },
        tooltip: {
          enabled: true,
          maxNumber: 20,
          order: 'value',
          onlyTooltipSeries: false,
        },
        dynamicParameters: [],
        auxiliaryLine: null,
        axes: {
          mainAxis: {},
          secondaryAxis: {},
          baseAxis: {},
        },
        theme: 'DEFAULT_COLOR',
        colors: [
          '#0781C3',
          '#FF7500',
          '#83BFF4',
          '#FFB86C',
          '#3FB27E',
          '#9BCC7E',
          '#F35352',
          '#FF9193',
          '#9D61C2',
          '#CAAED8',
          '#8FA500',
          '#BFD000',
        ],
      },
      colorAxis: null,
      limitInfo: {
        hasMoreData: false,
        dataLimit: 5000,
        hasMoreCol: false,
        colLimit: 1000,
      },
      series: [
        {
          name: '月累计销售金额',
          key: 'IdrrViORud',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 244164545.3535,
          threshold: null,
          t_idx: null,
        },
        {
          name: '本月目标',
          key: 'xfxvUcwqme',
          format: {
            specifier: ',.0f',
            suffix: '',
            isAuto: false,
            excelFormat: '#,###0""',
          },
          value: 559541246.9992,
          threshold: null,
          t_idx: null,
        },
        {
          name: '剩余',
          key: 'NAbJxbXhol',
          format: {
            suffix: '天',
            isAuto: false,
            excelFormat: '0"天"',
          },
          value: 20,
          threshold: null,
          t_idx: null,
        },
      ],
    },
    view: 'GRAPH',
    rawDataNotChanged: false,
    chartType: 'KPI_CARD',
    cardType: 'CHART',
    performanceAnalysis: {
      traceId: '85f8232f08f961e6',
      cdId: 'o62687c53f1fa4173b805bc6',
      cdName: '月累计销售金额',
      hitCache: 1,
      dataParse: {
        startTime: 1686452315277,
        endTime: 1686452315278,
        duration: 1,
      },
      askTime: {
        startTime: 1686452315251,
        endTime: 1686452315278,
        duration: 27,
      },
      otherTime: 26,
    },
  },
];

export const mockRequestList: ICardListRequest[] = [
  {
    label: '销售金额',
    value: 'tc5c0dbcef35b40739979149',
    id: 'tc5c0dbcef35b40739979149',
    payload: {
      name: '销售金额',
      filters: [
        {
          name: '统计日期',
          fdId: 's4421e1c6744743f3aab7453',
          dsId: 'vc6e963168a114f558c506f0',
          cdId: 'tc5c0dbcef35b40739979149',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-07', '2023-06-13'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '付款毛利率',
    value: 'x37b63326f41141c99c6621b',
    id: 'x37b63326f41141c99c6621b',
    payload: {
      name: '付款毛利率',
      filters: [
        {
          name: '时间',
          fdId: 'wc9606ce6815540189981973',
          dsId: 'c3f022dd060ce4af7afc20f7',
          cdId: 'x37b63326f41141c99c6621b',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '初上市价折扣率',
    value: 'c61543cfd52214d5d9a3887a',
    id: 'c61543cfd52214d5d9a3887a',
    payload: {
      name: '初上市价折扣率',
      filters: [
        {
          name: '时间',
          fdId: 'wc9606ce6815540189981973',
          dsId: 'c3f022dd060ce4af7afc20f7',
          cdId: 'c61543cfd52214d5d9a3887a',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '客单价',
    value: 'ceb4f661e1ee24d549dd8240',
    id: 'ceb4f661e1ee24d549dd8240',
    payload: {
      name: '客单价',
      filters: [
        {
          name: '日期',
          fdId: 'x7c5a9f7f07694730814c02a',
          dsId: 'jdd0fa49a113740dd8296325',
          cdId: 'ceb4f661e1ee24d549dd8240',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '年累计销售金额',
    value: 'da508482cd8c64a1bb86f9dd',
    id: 'da508482cd8c64a1bb86f9dd',
    payload: {
      name: '年累计销售金额',
      filters: [],
      dynamicParams: [],
    },
  },
  {
    label: '退款率(追溯)',
    value: 'a6049d95ece06456ba448b05',
    id: 'a6049d95ece06456ba448b05',
    payload: {
      name: '退款率(追溯)',
      filters: [
        {
          name: '时间',
          fdId: 'wc9606ce6815540189981973',
          dsId: 'c3f022dd060ce4af7afc20f7',
          cdId: 'a6049d95ece06456ba448b05',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '30天可用成本周转',
    value: 'nfee9db5a72554d12ac3e6c0',
    id: 'nfee9db5a72554d12ac3e6c0',
    payload: {
      name: '30天可用成本周转',
      filters: [
        {
          name: '时间整合',
          fdId: 'ubce0d7738b59447bb653a06',
          dsId: 'u7502c3f95bc54e39b15cd82',
          cdId: 'nfee9db5a72554d12ac3e6c0',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '会员GMV贡献率',
    value: 'q05e43943b98b4d299e8e96a',
    id: 'q05e43943b98b4d299e8e96a',
    payload: {
      name: '会员GMV贡献率',
      filters: [
        {
          name: '日期',
          fdId: 'x7c5a9f7f07694730814c02a',
          dsId: 'jdd0fa49a113740dd8296325',
          cdId: 'q05e43943b98b4d299e8e96a',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '差评率',
    value: 'r3697cda45b69476eb05ee3f',
    id: 'r3697cda45b69476eb05ee3f',
    payload: {
      name: '差评率',
      filters: [
        {
          name: '统计日期',
          fdId: 'd5e892ac78a684e38bd49593',
          dsId: 'vb877478cd2c14cb3998476f',
          cdId: 'r3697cda45b69476eb05ee3f',
          fdType: 'DATE',
          filterType: 'BT',
          filterValue: ['2023-06-04', '2023-06-10'],
        },
      ],
      dynamicParams: [],
    },
  },
  {
    label: '月累计销售金额',
    value: 'o62687c53f1fa4173b805bc6',
    id: 'o62687c53f1fa4173b805bc6',
    payload: {
      name: '月累计销售金额',
      filters: [],
      dynamicParams: [],
    },
  },
];

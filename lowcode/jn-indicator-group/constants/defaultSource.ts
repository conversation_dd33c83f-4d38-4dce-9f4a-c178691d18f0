export const DEFAULT_SOURCE = {
  children: {
    type: 'JSSlot',
    value: [
      {
        componentName: 'JNCascadeLineChart',
        id: 'node_oclj5g2g3w4',
        props: {
          series: [
            {
              name: '销售金额',
              key: 'IdrrViORud',
              data: [
                {
                  y: 8722239.4139,
                  colorBy: '',
                },
                {
                  y: 9234485.7297,
                  colorBy: '',
                },
                {
                  y: 106488422.2828,
                  colorBy: '',
                },
                {
                  y: 56332447.94,
                  colorBy: '',
                },
                {
                  y: 32986716.1043,
                  colorBy: '',
                },
                {
                  y: 35797390.0937,
                  colorBy: '',
                },
                {
                  y: 16721384.8775,
                  colorBy: '',
                },
              ],
              format: {
                specifier: ',.0f',
                suffix: '',
                isAuto: false,
                excelFormat: '#,###0""',
              },
              yAxis: 0,
              metric_additional: false,
              value: '0',
              numberValue: 0,
            },
            {
              name: '同比',
              key: 'EZgzfGJqdx',
              data: [
                {
                  y: 0.637939,
                  colorBy: '',
                },
                {
                  y: 0.606887,
                  colorBy: '',
                },
                {
                  y: 0.356509,
                  colorBy: '',
                },
                {
                  y: 0.454604,
                  colorBy: '',
                },
                {
                  y: 0.801676,
                  colorBy: '',
                },
                {
                  y: 0.556481,
                  colorBy: '',
                },
                {
                  y: 0.865847,
                  colorBy: '',
                },
              ],
              format: {
                specifier: ',.1%',
                suffix: '',
                divideDataBy: 1,
                isAuto: false,
                decimalPlaces: 1,
                excelFormat: '#,###0.0%""',
              },
              yAxis: 1,
              metric_additional: true,
              value: '0.0%',
              numberValue: 0,
            },
          ],
          serieColor: ['#489cff', '#8BD12E', '#fb8437', '#FFC300', '#997DED'],
          categories: [
            '2023-05-30',
            '2023-05-31',
            '2023-05-01',
            '2023-06-02',
            '2023-06-03',
            '2023-06-04',
            '2023-06-05',
          ],
          activityDays: [],
          componentStyle: {
            chartTitle: '默认图表名称',
            chartTitleShow: false,
            borderShow: true,
            rowCount: 5,
          },
          dataColumn: {
            dataColumn: {
              XAxis: {
                labelForAxis: true,
              },
              YAxis: {
                labelForAxis: true,
                lineStyle: 'solid',
                labelForNumeric: true,
                labelForNumericChoice: 'maxAndMin',
              },
            },
          },
        },
        docId: 'docljh004l0',
        hidden: false,
        title: '',
        isLocked: false,
        condition: true,
        conditionGroup: '',
        bindDataSet: '测试-123',
      },
    ],
    title: '图表插槽容器',
    id: 'jn_indicator_group_chart_slot',
  },
  componentStyle: {
    groupTitle: '数据概览',
    showChartControl: true,
    showTitle: true,
    rowCount: 5,
  },
  allDataColumn: {
    '1': {
      columns: [
        {
          key: '296',
          title: '目标',
          fieldId: 407,
          displayType: 'Single',
          dataType: 'text',
          value: 67243913.9215,
          dataField: '296',
          nickName: '目标',
          formatType: '#,###0',
          showControl: true,
        },
        {
          key: '297',
          title: '达成率',
          fieldId: 408,
          displayType: 'Single',
          dataType: 'text',
          value: 1.005035,
          dataField: '297',
          nickName: '达成率',
          formatType: '#,###0.0%',
          showControl: true,
        },
        {
          key: '298',
          title: '同比',
          fieldId: 409,
          displayType: 'Single',
          dataType: 'text',
          value: 0.584123,
          dataField: '298',
          nickName: '同比',
          formatType: '#,###0.0%',
          showControl: true,
        },
        {
          key: '299',
          title: '环比',
          fieldId: 410,
          displayType: 'Single',
          dataType: 'text',
          value: -0.679644,
          dataField: '299',
          nickName: '环比',
          formatType: '#,###0.0%',
          showControl: true,
        },
      ],
      mainField: '295',
      mainTitle: '销售金额',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'double',
    },
    '2': {
      columns: [
        {
          key: '302',
          title: '目标',
          fieldId: 401,
          displayType: 'Single',
          dataType: 'text',
          value: 0.581049,
          dataField: '302',
          nickName: '目标',
          formatType: '#,###0.0%',
          showControl: true,
        },
        {
          key: '303',
          title: '同期',
          fieldId: 402,
          displayType: 'Single',
          dataType: 'text',
          value: 0.630626,
          dataField: '303',
          nickName: '同期',
          formatType: '0.0%',
          showControl: true,
        },
      ],
      mainField: '301',
      mainTitle: '毛利率',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '3': {
      columns: [
        {
          key: '306',
          title: '同期',
          fieldId: 397,
          displayType: 'Single',
          dataType: 'text',
          value: 0.942492,
          dataField: '306',
          nickName: '同期',
          formatType: '0.0%',
          showControl: true,
        },
      ],
      mainField: '305',
      mainTitle: '初上市价折扣率',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '4': {
      columns: [
        {
          key: '309',
          title: '同比',
          fieldId: 385,
          displayType: 'Single',
          dataType: 'text',
          value: 0.013015,
          dataField: '309',
          nickName: '同比',
          formatType: '0.0%',
          showControl: true,
        },
        {
          key: '310',
          title: '环比',
          fieldId: 386,
          displayType: 'Single',
          dataType: 'text',
          value: -0.006533,
          dataField: '310',
          nickName: '环比',
          formatType: '0.0%',
          showControl: true,
        },
      ],
      mainField: '308',
      mainTitle: '客单价',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '5': {
      columns: [
        {
          key: '313',
          title: '全年目标',
          fieldId: 388,
          displayType: 'Single',
          dataType: 'text',
          value: 4798143279,
          dataField: '313',
          nickName: '全年目标',
          formatType: '#,###0',
          showControl: true,
        },
        {
          key: '314',
          title: '同比',
          fieldId: 389,
          displayType: 'Single',
          dataType: 'text',
          value: 0.68099,
          dataField: '314',
          nickName: '同比',
          formatType: '#,###0.0%',
          showControl: true,
        },
        {
          key: '315',
          title: '剩余',
          fieldId: 390,
          displayType: 'Single',
          dataType: 'text',
          value: 187,
          dataField: '315',
          nickName: '剩余',
          formatType: '0"天"',
          showControl: true,
        },
        {
          key: '316',
          title: '线上支付线下实销',
          fieldId: 391,
          displayType: 'Single',
          dataType: 'text',
          value: 2234410910.5289,
          dataField: '316',
          nickName: '线上支付线下实销',
          showControl: true,
        },
        {
          key: '317',
          title: '全年目标',
          fieldId: 392,
          displayType: 'Single',
          dataType: 'text',
          value: 4713181013,
          dataField: '317',
          nickName: '全年目标',
          showControl: true,
        },
      ],
      mainField: '312',
      mainTitle: '年累计销售金额',
      tooltip: true,
      tooltipText: '',
      progressKpiCard: true,
      distLink: '',
      YAxisShowType: 'single',
    },
    '6': {
      columns: [
        {
          key: '320',
          title: '同期',
          fieldId: 399,
          displayType: 'Single',
          dataType: 'text',
          value: 0.168958,
          dataField: '320',
          nickName: '同期',
          formatType: '0.0%',
          showControl: true,
        },
      ],
      mainField: '319',
      mainTitle: '退款率(追溯)',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '7': {
      columns: [
        {
          key: '323',
          title: '同期',
          fieldId: 395,
          displayType: 'Single',
          dataType: 'text',
          value: 51.353967,
          dataField: '323',
          nickName: '同期',
          formatType: '#,###0',
          showControl: true,
        },
      ],
      mainField: '322',
      mainTitle: '30天可用周转',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'double',
    },
    '8': {
      columns: [],
      mainField: '325',
      mainTitle: '会员贡献率',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '9': {
      columns: [],
      mainField: '327',
      mainTitle: '差评率',
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
    '10': {
      columns: [
        {
          key: '330',
          title: '本月目标',
          fieldId: 404,
          displayType: 'Single',
          dataType: 'text',
          value: 559996246.9992,
          dataField: '330',
          nickName: '本月目标',
          formatType: '#,###0',
          showControl: true,
        },
        {
          key: '331',
          title: '剩余',
          fieldId: 405,
          displayType: 'Single',
          dataType: 'text',
          value: 3,
          dataField: '331',
          nickName: '剩余',
          formatType: '0"天"',
          showControl: false,
        },
        {
          key: '332',
          title: '本月累计gmv',
          fieldId: 411,
          displayType: 'Single',
          dataType: 'text',
          value: 570623904.0562,
          dataField: '332',
          nickName: '本月累计gmv',
          showControl: true,
        },
        {
          key: '333',
          title: '本月目标',
          fieldId: 412,
          displayType: 'Single',
          dataType: 'text',
          value: 559996246.9992,
          dataField: '333',
          nickName: '本月目标',
          showControl: true,
        },
      ],
      mainField: '329',
      mainTitle: '月累计销售金额',
      progressKpiCard: true,
      tooltip: true,
      tooltipText: '',
      distLink: '',
      YAxisShowType: 'single',
    },
  },
  advanced: {
    nullFilled: '-',
    canExport: false,
  },
  style: {
    backgroundColor: '#ffffff',
    marginTop: '12px',
    padding: '16px',
  },
};

export const DEFAULT_OPTIONS = [
  {
    label: '混合指标卡数据集-35',
    value: 35,
  },
  {
    label: '混合指标卡数据集-38',
    value: 38,
  },
];

export const defaultDataColumnConfig35 = [
  {
    label: '指标卡1',
    value: 'mixCard-240',
  },
  {
    label: '指标卡2',
    value: 'mixCard-250',
  },
  {
    label: '指标卡3',
    value: 'mixCard-254',
  },
  {
    label: '指标卡4',
    value: 'mixCard-246',
  },
  {
    label: '指标卡5',
    value: 'mixCard-257',
  },
  {
    label: '指标卡6',
    value: 'mixCard-266',
  },
  {
    label: '指标卡7',
    value: 'mixCard-274',
  },
  {
    label: '指标卡8',
    value: 'mixCard-264',
  },
  {
    label: '指标卡9',
    value: 'mixCard-262',
  },
  {
    label: '指标卡10',
    value: 'mixCard-269',
  },
];
export const defaultDataColumnConfig38 = [
  {
    label: '指标卡1',
    value: 'mixCard-295',
  },
  {
    label: '指标卡2',
    value: 'mixCard-301',
  },
  {
    label: '指标卡3',
    value: 'mixCard-305',
  },
  {
    label: '指标卡4',
    value: 'mixCard-308',
  },
  {
    label: '指标卡5',
    value: 'mixCard-312',
  },
  {
    label: '指标卡6',
    value: 'mixCard-319',
  },
  {
    label: '指标卡7',
    value: 'mixCard-322',
  },
  {
    label: '指标卡8',
    value: 'mixCard-325',
  },
  {
    label: '指标卡9',
    value: 'mixCard-327',
  },
  {
    label: '指标卡10',
    value: 'mixCard-329',
  },
];

export const DATA_CONFIG_TYPE = {
  name: 'dataType',
  title: '数据类型',
  display: 'inline',
  isRequired: false,
  setter: {
    componentName: 'SelectSetter',
    props: {
      options: [
        { value: 'text', title: '文本' },
        { value: 'date', title: '日期' },
        { value: 'link', title: '链接' },
        { value: 'image', title: '图片' },
      ],
    },
  },
  extraProps: {
    display: 'inline',
  },
};

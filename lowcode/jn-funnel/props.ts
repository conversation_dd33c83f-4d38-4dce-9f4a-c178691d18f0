import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import {
  advancedItems,
  bindDatasetItems,
  componentStyleItems,
  globalStyleItems,
} from './propsConfig';
import { dataColumnItems } from './propsConfig/dataColumn';

export const JNFunnelProps: IPublicTypeFieldConfig[] = [
  // 绑定数据集
  {
    type: 'group',
    name: 'bindDataset',
    title: '数据集配置',
    items: bindDatasetItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局
  {
    type: 'group',
    name: 'componentStyle',
    title: '组件样式',
    items: componentStyleItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 数据列
  {
    type: 'group',
    name: 'dataColumn',
    title: '数据列',
    items: dataColumnItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 高级
  {
    type: 'field',
    name: 'advanced',
    title: '高级',
    setter: advancedItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局样式
  {
    type: 'group',
    name: 'style',
    title: '组件全局样式',
    items: globalStyleItems,
    extraProps: {
      display: 'accordion',
    },
  },
];

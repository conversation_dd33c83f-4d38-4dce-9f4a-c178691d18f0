import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

const columnsSetter = {
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'dataField',
              title: '数据字段',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: 'dataTitle',
              title: '数据标题',
              display: 'inline',
              isRequired: true,
              setter: 'StringSetter',
            },
          ],
        },
      },
    },
  },
};

export const dataColumnItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'dataColumn.columns',
    title: '字段集',
    setter: columnsSetter,
  },
];

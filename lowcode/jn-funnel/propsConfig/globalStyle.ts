import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const globalStyleItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'style',
    title: '组件全局样式',
    setter: {
      componentName: 'StyleSetter',
      props: {
        showModuleList: ['layout', 'background', 'border'], // 仅显示配置的模块
        layoutPropsConfig: {
          showDisPlayList: ['block', 'inline-block', 'none'],
          // isShowWidthHeight: false, // 是否显示宽度高度
        },
      },
    },
    extraProps: {
      display: 'plain',
    },
  },
];

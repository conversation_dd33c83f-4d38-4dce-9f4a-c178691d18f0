import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const componentStyleItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'componentStyle.titleControl',
    title: '标题显示',
    setter: 'BoolSetter',
    extraProps: {
      defaultValue: true,
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.chartTitle',
    title: '图表名称',
    setter: 'StringSetter',
    extraProps: {
      defaultValue: '通用漏斗图',
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.showMoreText',
    title: '更多显示',
    setter: {
      componentName: 'MixedSetter',
      props: {
        setters: ['StringSetter', { componentName: 'IconFontSetter', props: { type: 'node' } }],
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.linkText',
    title: '链接地址',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.chartGridTop',
    title: '图表上边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 1,
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.chartGridBottom',
    title: '图表下边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 1,
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.chartGridLeft',
    title: '图表左边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.chartGridRight',
    title: '图表右边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
    extraProps: {
      display: 'inline',
    },
  },
  // {
  //   type: 'field',
  //   name: 'componentStyle.labelPositionX',
  //   title: '标签水平位置(%)',
  //   setter: {
  //     componentName: 'NumberSetter',
  //     props: {
  //       units: '%',
  //     },
  //     initialValue: 88,
  //   },
  //   extraProps: {
  //     display: 'inline',
  //   },
  // },
];

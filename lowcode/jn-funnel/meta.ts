import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNFunnelSnippets } from './snippets';
import { JNFunnelProps } from './props';

const JNFunnelMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNFunnel',
  group: 'MelGeek组件',
  category: '图表类',
  title: '漏斗图',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b039.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNFunnel',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNFunnelProps,
    component: {},
  },
  snippets: JNFunnelSnippets,
};

export default JNFunnelMeta;

interface List {
  title: string;
  key: string //  52,
  // type: string // Single,
  // fieldId: number // 52
  show: boolean
  options?: any// 是否显示組件
}

interface CardDataWarp {
  defaultData?: any; // 传入的默认数据
  list: List[]; // 组件编辑的列表
}

const handleData = (data, i) => {
  let newData = {
    key: '',
    title: '',
    show: true,
    options: [],
  };

  newData.key = data[i].key;
  newData.title = data[i].title;

  data.map(item => {
    let _item = {
      title: item.title,
      value: item.key,
    }
    newData.options.push(_item);
  })

  return newData;
}

const dataBuild = (data) => {
  const backData: {
    imgData?: List,
    mainData?: List,
    describeData?: CardDataWarp,
    numberData?: CardDataWarp,
  } = {}

  backData.describeData = {
    list: [],
  }

  backData.numberData = {
    list: [],
  }

  if (data && data.length) {
    backData.imgData = { ...data[0], show: true };


    for (let i = 1; i < data.length; i++) {
      switch (i) {
        case 1:
          backData.mainData = handleData(data, i);
          break;
        case 2:
          backData.describeData.list.push({ ...data[i], show: true });
          break;
        case 3:
          backData.describeData.list.push({ ...data[i], show: true });
          break;
        default:
          backData.numberData.list.push({ ...data[i], show: true });
      }
    }
  }

  return backData;
}

export default dataBuild;

function show(arg0: any, show: any, arg2: boolean) {
  throw new Error("Function not implemented.");
}

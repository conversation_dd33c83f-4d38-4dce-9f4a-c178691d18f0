import { getTableOfPagination } from "../../src/services/cards/cardDataService";
import linkConfig from '../common/linkConfig';
import globalStyle from './propsConfig/globalStyle';
import dataConfig from "./propsConfig/dataconfig";
import dataTools from './utils/dataTools';
import { getSelectOption } from '../utils/dict';
import { getFilterArray } from '../../src/utils';

export default [
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '绑定数据集',
        items: [
            {
                name: 'id',
                title: '数据ID',
                extraProps: {
                    display: 'line',
                    setValue: (target, value) => {
                        if (value) {

                            target.parent.setPropValue('imgData', []);
                            target.parent.setPropValue('mainData', []);
                            target.parent.setPropValue('describeData.list', []);
                            target.parent.setPropValue('numberData.list', []);

                            getTableOfPagination({
                                id: value,
                                data: {
                                    filterValue: getFilterArray()
                                },
                            }).then(data => {
                                const delData = dataTools(data.data.headList);

                                target.parent.setPropValue('imgData', delData.imgData);
                                target.parent.setPropValue('mainData', delData.mainData);
                                target.parent.setPropValue('describeData.list', delData.describeData?.list);
                                target.parent.setPropValue('numberData.list', delData.numberData?.list);
                                target.parent.setPropValue('id', value);
                            })
                        }
                    }
                },
                setter: [
                    {
                        componentName: 'JNSelectSetter',
                        props: {
                            showSearch: true,
                            filterType: 'List',
                        }

                    },]
            }
        ],
    },
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '组件样式',
        items: [
            {
                name: 'componentStyle.titleShow',
                title: '标题显示',
                defaultValue: true,
                setter: 'BoolSetter',
            },
            {
                name: 'componentStyle.title',
                title: '标题',
                setter: 'StringSetter',
                condition: (target) => {
                    return target.getProps().getPropValue('componentStyle.titleShow') || false;
                },
            },
            {
                name: 'componentStyle.iconType',
                title: '图标',
                defaultValue: '',
                setter: 'IconFontSetter',
                condition: (target) => {
                    return target.getProps().getPropValue('componentStyle.titleShow') || false;
                },
            },
            //  跳转
            ...linkConfig,
        ]
    },
    globalStyle,
    dataConfig,
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '高级',
        items: [
            {
                name: 'componentStyle.displayShow',
                title: '隐藏空行',
                setter: 'BoolSetter',
            },
            {
                name: 'componentStyle.null',
                title: '单元格NULL填充',
                defaultValue: '-',
                setter: 'StringSetter',
            },
            {
                name: 'componentStyle.exportShow',
                title: '允许导出',
                setter: 'BoolSetter',
            },
            {
                name: 'componentStyle.filterShow',
                title: '组件筛选器',
                setter: 'BoolSetter',
            },
        ]
    },
    {
        name: "style",
        title: "样式",
        display: 'accordion',
        setter: {
            componentName: 'StyleSetter',
        },
    },
]

const globalStyle = {
    name: 'data',
    type: 'group',
    display: 'accordion',
    title: '全局样式',
    items: [
        {
            name: 'globalStyle.direction',
            title: '条目方向',
            defaultValue: 'portrait',
            setter: {
                componentName: 'RadioGroupSetter',
                props: {
                    options: [
                        {
                            title: '纵向',
                            value: 'portrait',
                        },
                        {
                            title: '横向',
                            value: 'transverse',
                        },
                    ],
                },
            },
        },
        {
            name: 'globalStyle.pagination',
            title: '分页器',
            defaultValue: false,
            setter: 'BoolSetter',
            condition: (target) => {
                return target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'pc' || false;
            },
        },
        {
            name: 'globalStyle.pageSize',
            title: '页行数',
            defaultValue: 10,
            setter: 'NumberSetter',
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.pagination') &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'pc' || false);
            },
        },
        {
            name: 'globalStyle.position',
            title: '分页器位置',
            defaultValue: '',
            setter: [
                {
                    componentName: 'SelectSetter',
                    props: {
                        options: [
                            {
                                title: '顶部',
                                value: 'top',
                            },
                            {
                                title: '底部',
                                value: 'bottom',
                            },
                            // {
                            //     title: '上左',
                            //     value: 'topLeft',
                            // },
                            // {
                            //     title: '上中',
                            //     value: 'topCenter',
                            // },
                            // {
                            //     title: '上右',
                            //     value: 'topRight',
                            // },
                            // {
                            //     title: '下左',
                            //     value: 'bottomLeft',
                            // },
                            // {
                            //     title: '下中',
                            //     value: 'bottomCenter',
                            // },
                            // {
                            //     title: '下右',
                            //     value: 'bottomRight',
                            // },
                        ],
                    }

                },
            ],
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.pagination') &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'pc' || false);
            },
        },
        {
            name: 'globalStyle.useTerminal',
            title: '运用终端',
            defaultValue: 'pc',
            setter: {
                componentName: 'RadioGroupSetter',
                props: {
                    options: [
                        {
                            title: 'PC端',
                            value: 'pc',
                        },
                        {
                            title: '移动端',
                            value: 'mobile',
                        },
                    ],
                },
            },
            condition: (target) => {
                return target.getProps().getPropValue('globalStyle.direction') === 'portrait' || false;
            },
        },
        {
            name: 'globalStyle.loadingMethod',
            title: '加载方式',
            defaultValue: 'clickLoad',
            setter: {
                componentName: 'RadioGroupSetter',
                props: {
                    options: [
                        {
                            title: '滚动加载',
                            value: 'rollingLoad',
                        },
                        {
                            title: '点击加载',
                            value: 'clickLoad',
                        },
                    ],
                },
            },
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'mobile' || false);
            },
        },
        {
            name: 'globalStyle.loadingNumber',
            title: '加载数量',
            defaultValue: 10,
            setter: 'NumberSetter',
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'mobile' || false);
            },
        },
        {
            name: 'globalStyle.loadingHeight',
            title: '内容区域高度',
            defaultValue: 400,
            setter: 'NumberSetter',
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'mobile' &&
                    target.getProps().getPropValue('globalStyle.loadingMethod') === 'rollingLoad' || false);
            },
        },
        {
            name: 'globalStyle.layout',
            title: '布局',
            defaultValue: 'compact',
            setter: {
                componentName: 'RadioGroupSetter',
                props: {
                    options: [
                        {
                            title: '宽松',
                            value: 'loose',
                        },
                        {
                            title: '紧凑',
                            value: 'compact',
                        },
                    ],
                },
            },
            condition: (target) => {
                return (
                    target.getProps().getPropValue('globalStyle.direction') === 'portrait' &&
                    target.getProps().getPropValue('globalStyle.useTerminal') === 'pc' || false);
            },
        },
        {
            name: 'globalStyle.showNumber',
            title: '显示数量',
            defaultValue: 2,
            setter: 'NumberSetter',
            condition: (target) => {
                return target.getProps().getPropValue('globalStyle.direction') === 'transverse' || false;
            },
        },
        {
            name: 'globalStyle.btnShow',
            title: '按钮显示',
            defaultValue: true,
            setter: 'BoolSetter',
            condition: (target) => {
                return target.getProps().getPropValue('globalStyle.direction') === 'transverse' || false;
            },
        },
        // {
        //     name: 'globalStyle.sortShow',
        //     title: '展示排序',
        //     defaultValue: true,
        //     setter: 'BoolSetter',
        // },
        // {
        //     name: 'globalStyle.sortType',
        //     title: '排序类型',
        //     defaultValue: 'rank',
        //     setter: {
        //         componentName: 'RadioGroupSetter',
        //         props: {
        //             options: [
        //                 {
        //                     title: '排名',
        //                     value: 'rank',
        //                 },
        //                 {
        //                     title: '序号',
        //                     value: 'serial ',
        //                 },
        //             ],
        //         },
        //     },
        //     condition: (target) => {
        //         return target.getProps().getPropValue('globalStyle.sortShow') || false;
        //     },
        // },
    ]
}

export default globalStyle;
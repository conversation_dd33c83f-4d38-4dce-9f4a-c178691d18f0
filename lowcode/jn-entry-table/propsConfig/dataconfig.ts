import { commonNumberColorProps } from "../../common/commonColorProps";

const dataConfig = {
    name: 'data1',
    type: 'group',
    display: 'accordion',
    title: '数据列',
    items: [
        {
            name: 'imgData',
            title: '图片',
            setter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'key',
                                title: '数据字段',
                                display: 'inline',
                                isRequired: true,
                                setter: [
                                    {
                                        componentName: 'SelectSetter',
                                        props: (target) => {
                                            const mainData = target.getProps().getPropValue('mainData');
                                            return {
                                                options: mainData?.options || [],
                                                showSearch: true,
                                            }
                                        }

                                    },
                                ]
                            },
                            {
                                name: 'show',
                                title: '是否显示',
                                display: 'inline',
                                setter: 'BoolSetter'
                            },
                        ],
                    },
                },
            },
            // condition: (target) => {
            //     return false;
            // },
        },
        {
            name: 'mainData',
            title: '主键',
            setter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'key',
                                title: '数据字段',
                                display: 'inline',
                                isRequired: true,
                                disabled: true,
                                setter: [
                                    {
                                        componentName: 'SelectSetter',
                                        props: (target) => {
                                            const mainData = target.getProps().getPropValue('mainData');
                                            return {
                                                options: mainData?.options || [],
                                                showSearch: true,
                                            }
                                        }

                                    },
                                ]
                            },
                            // {
                            //     name: 'title',
                            //     title: '别名',
                            //     display: 'inline',
                            //     isRequired: true,
                            //     setter: 'StringSetter'
                            // },
                            {
                                name: 'show',
                                title: '是否显示',
                                display: 'inline',
                                setter: 'BoolSetter'
                            },
                            {
                                name: 'tooltipShow',
                                title: '释义显示',
                                display: 'inline',
                                setter: 'BoolSetter',
                            },
                            {
                                name: 'tooltipText',
                                title: '释义内容',
                                display: 'inline',
                                initialValue: '',
                                setter: 'JNDicatorSetter',
                                condition: (target) => {
                                    return target.parent.getPropValue('tooltipShow') || false;
                                },
                            },
                        ],
                    },
                },
            },
        },
        {
            name: 'describeData.list',
            title: '描述字段',
            setter: {
                componentName: 'ArraySetter',
                props: {
                    itemSetter: {
                        componentName: 'ObjectSetter',
                        props: {
                            config: {
                                items: [
                                    {
                                        name: 'title',
                                        title: '标题',
                                        display: 'inline',
                                        isRequired: true,
                                        disabled: true,
                                        setter: 'StringSetter',
                                        extraProps: {
                                            display: 'inline',
                                        },
                                    },
                                    {
                                        name: 'key',
                                        title: '数据字段',
                                        display: 'inline',
                                        isRequired: true,
                                        setter: [
                                            {
                                                componentName: 'SelectSetter',
                                                props: (target) => {
                                                    const mainData = target.getProps().getPropValue('mainData');
                                                    return {
                                                        options: mainData?.options || [],
                                                        showSearch: true,
                                                    }
                                                }

                                            },
                                        ]
                                    },
                                    {
                                        name: 'show',
                                        title: '是否显示',
                                        display: 'inline',
                                        setter: 'BoolSetter'
                                    },
                                    {
                                        name: 'tooltipShow',
                                        title: '释义显示',
                                        display: 'inline',
                                        setter: 'BoolSetter',
                                    },
                                    {
                                        name: 'tooltipText',
                                        title: '释义内容',
                                        display: 'inline',
                                        initialValue: '',
                                        extraProps: {
                                            setValue: (target, val) => {
                                                const { jnIndicatorList } = window;
                                                jnIndicatorList && jnIndicatorList?.forEach(item => {
                                                    if (item.value === val) {
                                                        target.parent.setPropValue('title', item.label);
                                                    }
                                                })
                                            },
                                        },
                                        setter: 'JNDicatorSetter',
                                        condition: (target) => {
                                            return target.parent.getPropValue('tooltipShow') || false;
                                        },
                                    },
                                    ...commonNumberColorProps,
                                ],
                            },
                        },
                        initialValue: () => {
                            return {
                                title: '列标题',
                                show: true,
                            };
                        },
                    }
                }
            }
        },
        {
            name: 'numberData.list',
            title: '数值',
            setter: {
                componentName: 'ArraySetter',
                props: {
                    itemSetter: {
                        componentName: 'ObjectSetter',
                        props: {
                            config: {
                                items: [
                                    {
                                        name: 'title',
                                        title: '标题',
                                        display: 'inline',
                                        isRequired: true,
                                        disabled: true,
                                        setter: 'StringSetter',
                                        extraProps: {
                                            display: 'inline',
                                        },
                                    },
                                    {
                                        name: 'key',
                                        title: '数据字段',
                                        display: 'inline',
                                        isRequired: true,
                                        setter: [
                                            {
                                                componentName: 'SelectSetter',
                                                props: (target) => {
                                                    const mainData = target.getProps().getPropValue('mainData');
                                                    return {
                                                        options: mainData?.options || [],
                                                        showSearch: true,
                                                    }
                                                }

                                            },
                                        ]
                                    },
                                    {
                                        name: 'show',
                                        title: '是否显示',
                                        display: 'inline',
                                        setter: 'BoolSetter'
                                    },
                                    {
                                        name: 'tooltipShow',
                                        title: '释义显示',
                                        display: 'inline',
                                        setter: 'BoolSetter',
                                    },
                                    {
                                        name: 'tooltipText',
                                        title: '释义内容',
                                        display: 'inline',
                                        initialValue: '',
                                        extraProps: {
                                            setValue: (target, val) => {
                                                const { jnIndicatorList } = window;
                                                jnIndicatorList && jnIndicatorList?.forEach(item => {
                                                    if (item.value === val) {
                                                        target.parent.setPropValue('title', item.label);
                                                    }
                                                })
                                            },
                                        },
                                        setter: 'JNDicatorSetter',
                                        condition: (target) => {
                                            return target.parent.getPropValue('tooltipShow') || false;
                                        },
                                    },
                                    ...commonNumberColorProps,
                                ],
                            },
                        },
                        initialValue: () => {
                            return {
                                title: '列标题',
                                show: true,
                            };
                        },
                    }
                }
            }
        },
    ],
}

export default dataConfig;
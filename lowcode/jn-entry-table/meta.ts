// import { leftCardProps } from './defaultValue';  // 默认数据
import props from './props';

export const JNEntryTable = {
  componentName: 'JNEntryTable',
  title: '条目列表',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b021.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNEntryTable',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    // supports: {
    //   events: ['onSave', 'onRemove'],
    // },
  },
  snippets: [
    {
      title: '条目列表',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b021.png',
      schema: {
        componentName: 'JNEntryTable',
        props: {
          // componentStyle: {
          //   titleShow: true,
          //   title: '标题',
          // },
          globalStyle: {
            direction: 'portrait',
            position: 'bottom',
            useTerminal: 'pc',
            layout: 'compact',
            sortType: 'rank',
          },
        },
      },
    },
  ],
};

export default JNEntryTable;

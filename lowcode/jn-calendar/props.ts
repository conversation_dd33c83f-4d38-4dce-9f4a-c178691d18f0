import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { bindDatasetItems } from './propsConfig/bindDataset/bindDataset';

export const JNCalendarProps: IPublicTypeFieldConfig[] = [
  // 绑定数据集
  {
    type: 'group',
    name: 'specificDataset',
    title: '数据集配置',
    items: bindDatasetItems,
    extraProps: {
      display: 'accordion',
    },
  },
  {
    type: 'group',
    name: 'style',
    title: '组件全局样式',
    items: [
      {
        type: 'field',
        name: 'style',
        title: '组件全局样式',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
        extraProps: {
          display: 'plain',
        },
      },
    ],
    extraProps: {
      display: 'accordion',
    },
  },
];

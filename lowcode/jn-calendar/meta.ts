import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNCalendarSnippets } from './snippets';
import { JNCalendarProps } from './props';

const JNCalendarMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNCalendar',
  group: 'MelGeek组件',
  category: '表格类',
  title: '日历',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b037.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNCalendar',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNCalendarProps,
    component: {},
  },
  snippets: JNCalendarSnippets,
};

export default JNCalendarMeta;

import defaultValue from './defaultValue'; // 默认数据
import props from './props';

export const JNDashboardCardMeta = {
  componentName: 'JNDashboardCard',
  title: '指标卡-仪表盘',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b015.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['业务组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNDashboardCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '指标卡-仪表盘',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b015.png',
      schema: {
        componentName: 'JNDashboardCard',
        props: defaultValue,
      },
    },
  ],
};

export default JNDashboardCardMeta;

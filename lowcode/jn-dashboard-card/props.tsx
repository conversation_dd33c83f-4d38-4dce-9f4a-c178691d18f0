import _ from "lodash";
import { getPolarisDataById } from "../../src/services/cards/cardDataService";
import { defaultStyles } from './propsConfig/componentStyleConfig'
import { dataConfig } from './propsConfig/dataConfig'
import { dataToArray } from "./utils/dataTools";
import { getSelectOption } from "../utils/dict";
import { getFilterArray } from '../../src/utils';

export default [
  {
    name: 'data',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'id',
        title: '数据ID',
        extraProps: {
          display: 'line',
          setValue: (target, value) => {
            if (value) {
              // * 2. 主指标卡 main indicator card
              target.parent.setPropValue('mainIndicatorCard', {
                columns: [], isShow: true,
              });
              target.parent.setPropValue('singleGraphCard', {
                columns: [], isShow: true,
              });
              target.parent.setPropValue('dataCardList', {});
              getPolarisDataById({
                id: value, data: {
                  filterValue: getFilterArray()
                }
              }).then(data => {
                target.parent.setPropValue('componentStyle.title', data?.datasetName);
                const newData = dataToArray(_.get(data, 'data.headList') || []);
                target.parent.setPropValue('mainIndicatorCard', newData.mainIndicatorCard);
                target.parent.setPropValue('singleGraphCard', newData.singleGraphCard);
                target.parent.setPropValue('dataCardList', newData);
                target.parent.setPropValue('id', value);
              })
            }
          }
        },
        setter: [
          {
            componentName: 'JNSelectSetter',
            props: {
              showSearch: true,
              filterType: 'ObjectList',
            }
          },]
      }
    ],
  },
  // 默认的配置
  defaultStyles,
  // 数据配置
  dataConfig,
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'line',
        setter: 'StyleSetter',
      }
    ],
  },
]

export const dataColumnItems =
{
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'title',
              title: '名称',
              display: 'inline',
              isRequired: true,
              disabled: true,
              setter: 'StringSetter',
              extraProps: {
                display: 'inline',
              },
            },

            {
              name: 'key',
              title: '数据字段',
              display: 'inline',
              isRequired: true,
              setter: [
                {
                  componentName: 'SelectSetter',
                  props: (target) => {
                    let options = [];

                    const dataCardList = target.getProps().getPropValue('dataCardList');
                    dataCardList?.singleGraphCard?.columns?.map(item => {
                      options.push({
                        title: item.title,
                        value: item.key
                      });
                    });

                    dataCardList?.singleGraphCard?.columns?.map(item => {
                      options.push({
                        title: item.title,
                        value: item.key
                      });
                    });

                    return {
                      options,
                      showSearch: true,
                    }
                  }
                },
              ]
            },
            {
              name: 'arrow',
              title: '数据箭头',
              display: 'inline',
              setter: 'BoolSetter'
            },
            {
              name: 'show',
              title: '是否显示',
              display: 'inline',
              setter: 'BoolSetter'
            },
            {
              name: 'tooltipText',
              title: '绑定指标',
              display: 'inline',
              initialValue: '',
              extraProps: {
                setValue: (target, val) => {
                  const { jnIndicatorList } = window;
                  jnIndicatorList && jnIndicatorList?.forEach(item => {
                    if (item.value === val) {
                      target.parent.setPropValue('title', item.label);
                    }
                  })
                },
              },
              setter: 'JNDicatorSetter',
            },
          ],
        },
      },
      initialValue: () => {
        return {
          title: '列标题',
          formatType: 'text',
          dataIndex: 'dataIndex',
          show: true,
        };
      },
    }
  }
}

export const chartsColumnItems =
{
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'title',
              title: '名称',
              display: 'inline',
              isRequired: true,
              disabled: true,
              setter: 'StringSetter',
              extraProps: {
                display: 'inline',
              },
            },

            {
              name: 'key',
              title: '数据字段',
              display: 'inline',
              isRequired: true,
              setter: [
                {
                  componentName: 'SelectSetter',
                  props: (target) => {
                    let options = [];

                    const dataCardList = target.getProps().getPropValue('dataCardList');
                    dataCardList?.singleGraphCard?.columns?.map(item => {
                      options.push({
                        title: item.title,
                        value: item.key
                      });
                    });

                    dataCardList?.singleGraphCard?.columns?.map(item => {
                      options.push({
                        title: item.title,
                        value: item.key
                      });
                    });

                    return {
                      options,
                      showSearch: true,
                    }
                  }
                },
              ]
            },
          ],
        },
      },
      initialValue: () => {
        return {
          title: '列标题',
          dataIndex: 'dataIndex',
        };
      },
    }
  }
}
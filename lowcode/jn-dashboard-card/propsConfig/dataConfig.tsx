import React from 'react';
import { dataColumnItems, chartsColumnItems } from './dataColumnConfig'

export const dataConfig: any = {
  type: 'group',
  display: 'accordion',
  title: {
    label: '数据列',
  },
  items: [
    {
      name: '',
      title: '左侧指标',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'mainIndicatorCard.isShow',
                type: 'field',
                display: 'inline',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}>是否显示</div>,
                },
                setter: 'BoolSetter',
              },
              {
                name: 'mainIndicatorCard.columns',
                type: 'field',
                display: 'accordion',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}> 主指标卡</div>,
                },
                setter: dataColumnItems,
                condition: (target) => {
                  const show = target.getProps().getPropValue('mainIndicatorCard.isShow');
                  return show || false;
                },
              },
            ]
          }
        }
      }
    },
    {
      name: '',
      title: '可视化单图',
      setter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'singleGraphCard.isShow',
                type: 'field',
                display: 'inline',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}>是否显示</div>,
                },
                setter: 'BoolSetter',
              },
              {
                name: 'singleGraphCard.title',
                type: 'field',
                display: 'inline',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}>标题</div>,
                },
                setter: 'StringSetter',
                condition: (target) => {
                  const show = target.getProps().getPropValue('singleGraphCard.isShow');
                  return show || false;
                },
              },
              {
                name: 'singleGraphCard.color',
                type: 'field',
                display: 'inline',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}>颜色配置</div>,
                },
                setter: 'ColorSetter',
                condition: (target) => {
                  const show = target.getProps().getPropValue('singleGraphCard.isShow');
                  return show || false;
                },
              },
              {
                name: 'singleGraphCard.columns',
                type: 'field',
                display: 'accordion',
                title: {
                  label: <div style={{ fontSize: 12, padding: '6px 0' }}>数据列</div>,
                },
                setter: chartsColumnItems,
                condition: (target) => {
                  const show = target.getProps().getPropValue('singleGraphCard.isShow');
                  return show || false;
                },
              },
            ]
          }
        }
      }
    },
  ]
}

import linkConfig from '../../common/linkConfig'

// 默认样式  名称  图标  汇总
export const defaultStyles = {
  name: 'data',
  type: 'group',
  display: 'accordion',
  title: {
    label: '组件样式',
  },
  items: [
    {
      name: 'componentStyle.titleShow',
      title: '标题显示',
      defaultValue: true,
      display: 'inline',
      setter: 'BoolSetter',
    },
    {
      name: 'componentStyle.title',
      title: '名称',
      setter: 'StringSetter',
      condition: (target) => {
        return target.getProps().getPropValue('componentStyle.titleShow') || false;
      },
    },
    {
      name: 'componentStyle.iconType',
      title: '图标',
      setter: 'IconFontSetter',
      condition: (target) => {
        return target.getProps().getPropValue('componentStyle.titleShow') || false;
      },
    },
    {
      name: 'componentStyle.align',
      title: '对齐方式',
      defaultValue: 'leftJustifying',
      setter: [
        {
          componentName: 'SelectSetter',
          props: {
            options: [
              {
                label: '左对齐',
                value: 'leftJustifying',
              },
              {
                label: '水平对齐',
                value: 'HorizontalAlignment',
              },
              {
                label: '右对齐',
                value: 'rightJustifying',
              },
            ]
          }
        },
      ]

    },
    //  跳转
    ...linkConfig,
  ],
}



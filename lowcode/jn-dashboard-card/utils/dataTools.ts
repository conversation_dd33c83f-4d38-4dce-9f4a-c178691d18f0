// 对数据进行分组， 默认第一个是表头，后面的是自动平铺
import _, { List } from "lodash";
import { dataColumnItems } from "../propsConfig/dataColumnConfig";
interface Column {
  title: string;
  key: string //  52,
  type: string // Single,
  fieldId: number // 52
  show: true // 是否显示組件
}

interface CardDataWarp {
  defaultData?: any; // 传入的默认数据
  isShow: boolean; // boolean 是否显示
  columns: Column[]; // 组件编辑的列表
}
/**
 * 1. 可视化单图 single graph
 * 2. 页签指标 Tab Indicator
 * 3. 主指标卡 main indicator card
 * 4. 主指标卡 // array Other indicator cards

 * @returns
 */

export const dataBuild = (data) => {
  const defaultArr: Column[] = [];
  defaultArr.push({ ...data, show: true });

  if (data.children) {
    data.children.map(item => {
      defaultArr.push({ ...item, show: true });
    });

  }
  if (data.assistants) {
    data.assistants.map(item => {
      defaultArr.push({ ...item, show: true });
    });
  }
  return {
    defaultData: data,
    isShow: true,
    columns: defaultArr
  }
}

export const dataToArray = (data) => {
  const backData: {
    mainIndicatorCard?: CardDataWarp
    singleGraphCard?: CardDataWarp
  } = {}

  backData.mainIndicatorCard = {
    isShow: true,
    columns:[],
    defaultData:[],
  }

  for (let i = 0; i < data.length; i++) {
    if (i === data.length - 1) {
      backData.singleGraphCard = dataBuild(data[i]);
    } else {
      backData.mainIndicatorCard.columns.push(...dataBuild(data[i]).columns);
    }
  }

  return backData;
}
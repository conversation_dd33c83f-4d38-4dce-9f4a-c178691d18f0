// =========================蕉内 甜甜圈指标卡组件
import props from './propsConfig/props';

export const JNDoughnutCard = {
  componentName: 'JNDoughnutCard',
  title: '指标卡-甜甜圈',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b020.png',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNDoughnutCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    // supports: {
    //   events: ['onSave', 'onRemove'],
    // },
  },
  snippets: [
    {
      title: '指标卡-甜甜圈',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b020.png',
      schema: {
        componentName: 'JNDoughnutCard',
        props: {
          style: {
            width: '400px',
            height: '400px',
          },
        },
      },
    },
  ],
};

export default JNDoughnutCard;

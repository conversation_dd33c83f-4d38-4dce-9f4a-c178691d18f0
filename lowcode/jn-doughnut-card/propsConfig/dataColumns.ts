const dataColumnItems =
{
    componentName: 'ArraySetter',
    props: {
        itemSetter: {
            componentName: 'ObjectSetter',
            props: {
                config: {
                    items: [
                        {
                            name: 'name',
                            title: '名称',
                            display: 'inline',
                            isRequired: true,
                            setter: 'StringSetter',
                        },
                        {
                            name: 'color',
                            title: '颜色',
                            display: 'inline',
                            isRequired: true,
                            setter: 'ColorSetter'
                        },
                        {
                            name: 'textColor',
                            title: '字体颜色(标签位置内部时生效)',
                            display: 'inline',
                            setter: 'ColorSetter'
                        },
                        {
                            name: 'defalutShow',
                            title: '默认展示',
                            display: 'inline',
                            setter: 'BoolSetter',
                            condition: (target) => {
                                return target.getProps().getPropValue('legend.show') && true;
                            },
                        },
                    ],
                },
            },
            extraProps: {
                renderFooter: 1111
            },
            initialValue: () => {
                return {
                    color: '#333',
                    defalutShow: true,
                };
            },
        }
    }
}

export default dataColumnItems;
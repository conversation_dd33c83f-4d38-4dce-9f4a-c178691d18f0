import { creatFilter, updateFilter } from "../../src/services/cards/cardDataService";
import _ from 'lodash';
import { setGlobalTimeFilterRange, setGlobalTimeRange } from '../utils/config';
import effectRanges from '../common/filters/effectRanges';
import { mockData } from "../common/filters/defaultValueMock";

// 提取url参数
export function getUrlParam(name) {
  return (
    decodeURIComponent(
      (new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`, 'i').exec(location.search) || [
        undefined,
        '',
      ])[1].replace(/\+/g, '%20'),
    ) || null
  );
}

const map = {
  time: 'BT',
  singleTime: 'BT',
  select: 'IN',
  multipleSelect: 'IN',
  treeSelect: 'IN',
  interval: 'BT',
}

export const columnsField = {
  type: 'field',
  name: 'filters',
  title: '筛选器列表',
  extraProps: {
    display: 'accordion',
  },
  setter: {
    componentName: 'ArraySetter',
    props: {
      itemSetter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: "type",
                title: "观远过滤器",
                isRequired: true,
                extraProps: {
                  setValue: (target, value) => {
                    target.parent.setPropValue('defalutKey', '');
                    target.parent.setPropValue('filterType', '');

                    // 创建一个北四的过滤器并将id设置到北四
                    const filterId =  target.parent.getPropValue('filterId');
                    let runFn = creatFilter
                    const params = {
                      cardId: value,
                      pageId: getUrlParam('id') || '1' // 临时写死
                    }
                    if(filterId){
                      runFn = updateFilter;
                      params.id = filterId;
                    }
                    runFn(params).then((result: any) => {
                      const selectorType = _.get(result, 'filterMeta.content.selectorType');
                      const filterType = _.get(result, 'filterMeta.content.filterType');
                      const filterName = _.get(result, 'filterMeta.name');

                      target.parent.setPropValue('filterType', filterType);
                      target.parent.setPropValue('label', filterName);
                      target.parent.setPropValue('filterId', result?.id);

                      switch (selectorType) {
                        case "TIME_MACRO":
                          target.parent.setPropValue('selectorType', 'time');
                          target.parent.setPropValue('tiemFrame', ['昨天', '上周', '本月至昨天', '日', '周', '月', '年', '自定义']);
                          target.parent.setPropValue('defalutKey', 'yesterday');
                          setGlobalTimeFilterRange({
                            key: map.time,
                            value: 'yesterday',
                            filterId: result?.id
                          })
                          break;
                        case "DS_INTERVAL":
                          target.parent.setPropValue('selectorType', 'interval');
                          break;

                        case "DS_ELEMENTS":
                          target.parent.setPropValue('selectorType', 'multipleSelect');
                          break;

                        case "TREE":
                          target.parent.setPropValue('selectorType', 'treeSelect');
                          break;

                        case "TEXT_MATCH":
                          target.parent.setPropValue('selectorType', 'textMatch');
                          break;

                        default: {
                          target.parent.setPropValue('selectorType', '');
                        }
                      }

                    })
                  }
                },
                setter: [
                  {
                    componentName: "CascaderSelectSetter",
                    props: {

                    }
                  }
                ]
              },
              {
                name: 'width',
                title: '宽度',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'filterId',
                title: '筛选器ID',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'selectorType',
                title: '数据类型',
                display: 'inline',
                // initialValue: 'time',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'time', title: '时间' },
                      { value: 'singleTime', title: '时间单选' },
                      { value: 'select', title: '单选' },
                      { value: 'multipleSelect', title: '多选' },
                      { value: 'treeSelect', title: '树状选择' },
                      { value: 'interval', title: '范围' },
                      { value: 'textMatch', title: '条件' },
                      { value: 'dateTime', title: '时分秒' },
                    ],
                  },
                },
              },
              {
                name: 'multiple',
                title: '是否多选',
                display: 'inline',
                setter: 'BoolSetter',
                defaultValue: true,
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'treeSelect';
                },
              },
              // {
              //   name: 'hasPath',
              //   title: '是否带路径',
              //   display: 'inline',
              //   setter: 'BoolSetter',
              //   defaultValue: true,
              //   condition: (target) => {
              //     const selectorType = target.parent.getPropValue('selectorType');
              //     return selectorType === 'treeSelect';
              //   },
              // },
              {
                name: 'label',
                title: '名称',
                display: 'inline',
                setter: 'StringSetter'
              },
              {
                name: 'placeholder',
                title: '占位符',
                display: 'inline',
                setter: 'StringSetter',
              },
              {
                name: 'tiemFrame',
                title: '时间范围',
                display: 'inline',
                setter: 'TiemCheckboxSetter',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'time';
                },
              },
              // 时间默认值
              {
                name: 'defalutKey',
                title: '默认值',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'time';
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const effectRanges = target.parent.getPropValue('effectRanges');

                    setGlobalTimeFilterRange({
                      key: map[selectorType],
                      value,
                      filterId,
                      effectRanges,
                    })
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      {
                        value: 'yesterday',
                        title: '昨天',
                      },
                      {
                        value: 'today',
                        title: '今日',
                      },
                      {
                        value: 'days7',
                        title: '近7天',
                      },
                      {
                        value: 'days30',
                        title: '近30天',
                      },
                      {
                        value: 'lastWeek',
                        title: '上周',
                      },
                      {
                        value: 'thisWeekToYesterday',
                        title: '本周至昨天',
                      },
                      {
                        value: 'thisMonthToYesterday',
                        title: '本月至昨天',
                      },
                      {
                        value: 'thisYearToYesterday',
                        title: '本年至昨天',
                      },
                      {
                        value: 'presaleToYesterday',
                        title: '预售至昨天',
                      },
                      {
                        value: 'saleToYesterday',
                        title: '正式至昨天'
                      },
                      {
                        value: 'futureSeven',
                        title: '未来7天'
                      },
                      {
                        value: 'futureThirty',
                        title: '未来30天'
                      },
                      {
                        value: 'futureNinety',
                        title: '未来90天'
                      },
                      {
                        value: 'futureThirtyEighty',
                        title: '未来180天',
                      },
                      {
                        value: 'doubleEleven',
                        title: '双11备货',
                      },
                      {
                        value: 'doubleElevenPresale',
                        title: '双11预售起',
                      },
                      {
                        value: 'doubleElevenOfficial',
                        title: '双11正式起',
                      },
                      {
                        value: 'doubleTwelve',
                        title: '双12备货',
                      },
                    ],
                  },
                }
              },
              {
                name: 'disabledDateState',
                title: '开启未来时间',
                setter: 'BoolSetter',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'time';
                },
              },
              // 时分秒默认方式
              {
                name: 'defalutType',
                title: '默认方式',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'fixedValue', title: '固定值' },
                      { value: 'dynamicValue', title: '动态日期' },
                    ],
                  },
                  initialValue: 'fixedValue',
                },
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType === 'dateTime';
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');
                    const defalutKey = target.parent.getPropValue('defalutKey');

                    setGlobalTimeRange({
                      key: map[selectorType],
                      filterId,
                      value: defalutKey,
                      defalutType
                    })
                  },
                },
              },
              {
                name: 'defalutKey',
                title: '默认值',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  const defalutType = target.parent.getPropValue('defalutType');
                  return selectorType && selectorType === 'dateTime' && defalutType === 'dynamicValue';
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');
                    const currentFilter = {
                      key: map[selectorType],
                      filterId,
                      value,
                      defalutType
                    }

                    setGlobalTimeRange(currentFilter)
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: mockData,
                  },
                },
              },

              // 其他默认值
              {
                name: 'defalutType',
                title: '默认方式',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'fixedValue', title: '固定值' },
                      { value: 'firstValue', title: '下拉列表中第一个' },
                    ],
                  },
                  initialValue: 'fixedValue',
                },
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  return selectorType !== 'time' && selectorType !== 'dateTime';
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');
                    const defalutKey = target.parent.getPropValue('defalutKey');

                    setGlobalTimeRange({
                      key: map[selectorType],
                      filterId,
                      value: defalutKey,
                      defalutType
                    })
                  },
                },
              },
              {
                name: 'defalutKey',
                title: '默认值',
                condition: (target) => {
                  const selectorType = target.parent.getPropValue('selectorType');
                  const defalutType = target.parent.getPropValue('defalutType');

                  return selectorType && (!['time'].includes(selectorType)) && defalutType === 'fixedValue';
                  // return selectorType && (!['time'].includes(selectorType));
                },
                extraProps: {
                  setValue: (target, value) => {
                    const filterId = target.parent.getPropValue('filterId');
                    const selectorType = target.parent.getPropValue('selectorType');
                    const defalutType = target.parent.getPropValue('defalutType');
                    const currentFilter = {
                      key: map[selectorType],
                      filterId,
                      value,
                      defalutType
                    }
                    // 针对tree有带path的情况， 需要做数据切割， 分隔符设为@_@
                    if (selectorType === 'treeSelect') {
                      currentFilter.pathFilterRegex = '@_@'
                    }
                    setGlobalTimeRange(currentFilter)
                  },
                },
                setter: 'StringSetter'
              },
              {
                name: 'show',
                title: '是否显示',
                setter: 'BoolSetter',
                defaultValue: true,
                condition: (target) => {
                  return target.parent.getPropValue('selectorType') !== 'time';
                }
              },
              effectRanges,
            ],
          },
        },
        initialValue: () => {
          return {
            selectorType: '',
            width: 200,
          };
        },
      },
    },
  },
};

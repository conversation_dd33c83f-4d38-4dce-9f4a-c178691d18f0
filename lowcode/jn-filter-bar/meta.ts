import { IComponentDescription } from '../types/index';

import { props } from './props';

function JNFilterBarFilter(prop: any) {
  const ignorePropNames: string[] = ['dataSource'];
  return !ignorePropNames.includes(prop?.name);
}

export const JNFilterBarMeta: IComponentDescription = {
  componentName: 'JNFilterBar',
  title: '全局筛选器',
  isHidden: true,
  docUrl: '',
  icon: 'https://cdn-h5.bananain.cn/icons/b025.png',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '表格类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNFilterBar',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
      disableBehaviors: ['copy'],
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: '全局筛选器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b025.png',
      schema: {
        componentName: 'JNFilterBar',
        props: {
          settingButtons: true,
          columns: [],
          styleType: '通用',
        },
      },
    },
  ],
};

export default JNFilterBarMeta;

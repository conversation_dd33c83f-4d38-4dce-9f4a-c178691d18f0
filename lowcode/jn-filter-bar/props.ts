// 加载进行
// import { creatFilter } from "../../src/services/cards/cardDataService";
// import { getFilterOptions } from "../utils/dict";
// import { setGlobalTimeRange, setGlobalTimeFilterId } from "../utils/config";
import { columnsField, getUrlParam } from "./columns-field";


if (!window.pageConfig || !window.pageConfig.timeRange) {
  window.pageConfig = {
    timeRange: {
      key: 'days7',
    }
  }
}

export const props = [
  // {
  //   type: 'select',
  //   name: '',
  //   title: '组件样式',
  //   display: 'accordion',
  //   setter: (field, target) => {
  //     return {
  //       componentName: 'ObjectSetter',
  //       display: 'inline',
  //       props: {
  //         config: {
  //           items: [
  //             // {
  //             //   name: 'styleType',
  //             //   title: '布局方式',
  //             //   initialValue: '通用',
  //             //   setter: {
  //             //     componentName: 'RadioGroupSetter',
  //             //     props: { options: ['通用', '简约'] },
  //             //   },
  //             // },
  //             {
  //               name: 'title',
  //               title: '组件标题',
  //               setter: 'StringSetter',
  //               condition: (target) => {
  //                 return target.parent.getPropValue('styleType') === '通用';
  //               },
  //             },
  //             {
  //               name: 'fontSize',
  //               title: '标题大小(px)',
  //               setter: 'NumberSetter',
  //               condition: (target) => {
  //                 return target.parent.getPropValue('styleType') === '通用';
  //               },
  //             }
  //           ],
  //         },
  //       },
  //     }
  //   },
  // }, 
  columnsField, {
    name: "style",
    title: "行内样式",
    display: 'accordion',
    setter: {
      componentName: 'StyleSetter',
      // props: {
      //   showModuleList: ['layout'],
      // }
    },
  },]


/*
 * @Author: kiki
 * @Date: 2023-10-10 10:37:12
 * @LastEditTime: 2024-05-10 17:12:23
 * @LastEditors: kiki
 * @Description:
 */
export default [
  {
    name: 'title',
    title: '容器标题',
    defaultValue: '容器标题',
    setter: 'StringSetter',
  },
  {
    name: 'icon',
    title: '容器图标',
    defaultValue: '',
    setter: 'IconFontSetter',
  },
  {
    name: 'headerSize',
    title: '标题大小',
    defaultValue: 'big',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          {
            title: '大',
            value: 'big',
          },
          {
            title: '小',
            value: 'small',
          },
        ],
      },
    },
  },
  {
    name: 'content',
    title: '容器内容',
    setter: 'SlotSetter', // 可支持拖拽组件或可支持外部传入组件，此项需配置为 SlotSetter
  },
  {
    name: 'tooltipControl',
    title: '说明开关',
    setter: 'BoolSetter',
  },
  {
    name: 'tooltipType',
    title: '说明类型',
    defaultValue: 'simpleText',
    condition: (target) => {
      return !!target.getProps().getPropValue('tooltipControl');
    },
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            title: '短文本释义',
            value: 'simpleText',
          },
          {
            title: '长文本释义',
            value: 'longText',
          },
          {
            title: '文档释义',
            value: 'doc',
          },
        ],
      },
    },
  },
  {
    name: 'tooltipTitle',
    title: '说明标题',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('tooltipControl');
      const tooltipType = target.getProps().getPropValue('tooltipType');
      const typePermit = tooltipType === 'longText' || tooltipType === 'doc';

      return isShow && typePermit;
    },
    setter: 'StringSetter',
  },
  {
    name: 'tooltip',
    title: '说明内容',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('tooltipControl');
      const tooltipType = target.getProps().getPropValue('tooltipType');
      const typePermit = tooltipType === 'simpleText' || tooltipType === 'longText';

      return isShow && typePermit;
    },
    setter: 'TextAreaSetter',
  },
  {
    name: 'tooltipDocIcon',
    title: '说明文档图标',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('tooltipControl');
      const tooltipType = target.getProps().getPropValue('tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && typePermit;
    },
    setter: 'IconFontSetter',
  },
  {
    name: 'tooltipDocUrl',
    title: '说明文档链接',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('tooltipControl');
      const tooltipType = target.getProps().getPropValue('tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && typePermit;
    },
    setter: 'TextAreaSetter',
  },
  {
    name: 'moreOpen',
    title: '更多分析',
    setter: 'BoolSetter',
  },
   {
    name: 'linkShowText',
    title: '更多文本',
    setter: 'StringSetter',
    condition: (target) => {
      return target.getProps().getPropValue('moreOpen');
    },
  },
  {
    name: 'linkConfig',
    title: '更多分析',
    setter: 'LinkBoxSetter',
    condition: (target) => {
      return target.getProps().getPropValue('moreOpen');
    },
  },
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'line',
        setter: 'StyleSetter',
      },
    ],
  },
  {
    name: 'foldSettings',
    type: 'group',
    display: 'accordion',
    title: {
      label: '设置组件折叠',
    },
    items: [
      {
        name: 'foldContent',
        title: '折叠功能',
        setter: {
          componentName: 'SlotSetter',
          isRequired: true,
          title: '折叠组件坑位',
          initialValue: {
            type: 'JSSlot',
            value: [],
          },
        }
      },
      {
        name: 'foldInitStatus',
        title: '初始状态',
        defaultValue: true,
        condition: (target) => {
          return !!target.getProps().getPropValue('foldContent');
        },
        setter: {
          componentName: 'RadioGroupSetter',
          props: {
            options: [
              {
                title: '收起',
                value: true,
              },
              {
                title: '展开',
                value: false,
              },
            ],
          },
        },
      }]
  },
];

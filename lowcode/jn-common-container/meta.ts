import props from './props';

const JNCommonContainerSnippets = {
  title: '通用型容器',
  style: {
    marginTop: '12px',
    padding: '16px',
  },
  content: {
    type: 'JSSlot', // 可支持拖拽组件或可支持外部传入组件，此项需配置为 JSSlot
    id: 'jn_common_a001',
  },
  moreOpen: false,
  linkShowText: '更多分析',
};

const JNCommonContainerMeta = [
  {
    componentName: 'JNCommonContainer',
    title: '通用型容器',
    category: '布局容器类',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNCommonContainer',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: false,
        nestingRule: {},
      },
      props,
      // supports: {
      //   events: ['onSave', 'onRemove'],
      // },
    },
    snippets: [
      {
        title: '通用型容器',
        screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
        schema: {
          componentName: 'JNCommonContainer',
          props: JNCommonContainerSnippets,
        },
      },
    ],
  },
];

export default JNCommonContainerMeta;

import props from './props';
import { DEFAULT_SOURCE } from './constants/defaultSource';

const JNHeatMapSnippets = [
  {
    title: '通用热力图',
    screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b033.png',
    schema: {
      componentName: 'JNHeatMap',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNHeatMapMeta = {
  componentName: 'JNHeatMap',
  title: '通用热力图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b033.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNHeatMap',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNHeatMapSnippets,
};

export default { ...JNHeatMapMeta, JNHeatMapSnippets };

import React from 'react';

const defaultStyle = {
  fontFamily: 'font-fz-bold',
  fontSize: 12,
};

export const commonFieldConfig = (namePrefix: string) => {
  return [
    {
      name: `chartConfig.${namePrefix}.fieldTitleShow`,
      title: '标题显示',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: 'BoolSetter',
    },
    {
      name: `chartConfig.${namePrefix}.fieldTitle`,
      title: '标题名称',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: 'StringSetter',
    },
    {
      name: `chartConfig.${namePrefix}.fieldTitlePosition`,
      title: '标题位置',
      defaultValue: 'center',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { title: '起始', value: 'start' },
            { title: '中心', value: 'center' },
            { title: '末尾', value: 'end' },
          ],
          showSearch: true,
          allowClear: true,
          placeholder: '选择位置',
        },
      },
    },
    {
      name: `chartConfig.${namePrefix}.fieldTitleGap`,
      title: '标题间距',
      defaultValue: 30,
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: 'NumberSetter',
    },
    {
      name: `chartConfig.${namePrefix}.dataField`,
      title: '选择字段',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: {
        componentName: 'SelectSetter',
        props: (target) => {
          return {
            options: target.getProps().getPropValue('storeConfig.dimOptions'),
            showSearch: true,
            allowClear: true,
          };
        },
      },
    },
    {
      name: `chartConfig.${namePrefix}.labelShow`,
      title: '标签显示',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      defaultValue: true,
      setter: 'BoolSetter',
    },
    {
      name: `chartConfig.${namePrefix}.labelMargin`,
      title: '标签间距',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      defaultValue: 8,
      setter: 'NumberSetter',
    },
    {
      name: `chartConfig.${namePrefix}.labelRotate`,
      defaultValue: 0, // 默认不旋转
      title: '标签旋转(Deg)',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: 'NumberSetter',
    },
    {
      name: `chartConfig.${namePrefix}.labelAlign`,
      defaultValue: 'center',
      title: '标签对齐',
      condition: (target) => {
        return !!(target.getProps().getPropValue('chartConfig.configType') === namePrefix);
      },
      setter: {
        componentName: 'CustomRadioGroupSetter',
        props: {
          dataSource: [
            { label: '居左', value: 'right' },
            { label: '居中', value: 'center' },
            {
              label: '居右',
              value: 'left',
            },
          ],
        },
      },
    },
  ];
};

export const chartConfigItems = [
  {
    name: 'chartConfig.isShow',
    title: '配置显隐',
    defaultValue: true,
    setter: 'BoolSetter',
  },
  {
    name: 'chartConfig.configType',
    title: '配置切换',
    defaultValue: 'dimConfig',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          { title: '维度配置', value: 'dimConfig' },
          { title: '对比配置', value: 'ratioConfig' },
        ],
      },
    },
  },
  {
    name: 'chartConfig.dimConfig',
    title: <div style={defaultStyle}>维度配置</div>,
    condition: (target) => {
      return !!(target.getProps().getPropValue('chartConfig.configType') === 'dimConfig');
    },
  },
  ...commonFieldConfig('dimConfig'),
  {
    name: 'chartConfig.ratioConfig',
    title: <div style={defaultStyle}>对比配置</div>,
    condition: (target) => {
      return !!(target.getProps().getPropValue('chartConfig.configType') === 'ratioConfig');
    },
  },
  ...commonFieldConfig('ratioConfig'),
];

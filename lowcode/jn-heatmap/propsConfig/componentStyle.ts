import { DEFAULT_DATA_FORMAT_TYPE } from '../../common/config/dataFormatType';

export const componentStyleItems = [
  {
    name: 'componentStyle.chartTitleShow',
    title: '标题显示',
    defaultValue: false,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.chartTitle',
    title: '图表名称',
    defaultValue: '默认名称-通用热力图',
    setter: 'StringSetter',
  },
  {
    name: 'componentStyle.showMoreText',
    title: '更多显示',
    defaultValue: '默认名称-更多显示',
    setter: {
      componentName: 'MixedSetter',
      props: {
        setters: ['StringSetter', { componentName: 'IconFontSetter', props: { type: 'node' } }],
      },
    },
  },
  {
    name: 'componentStyle.linkText',
    title: '链接地址',
    setter: 'StringSetter',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.showMoreText');
    },
  },
  {
    name: 'componentStyle.chartHeight',
    title: '图表高度',
    defaultValue: 520,
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.chartGrid',
    title: '图表间距',
    defaultValue: true,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.chartGridTop',
    title: '图表上边距',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.chartGrid');
    },
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
    },
  },
  {
    name: 'componentStyle.chartGridBottom',
    title: '图表下边距',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.chartGrid');
    },
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
    },
  },
  {
    name: 'componentStyle.chartGridLeft',
    title: '图表左边距',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.chartGrid');
    },
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
    },
  },
  {
    name: 'componentStyle.chartGridRight',
    title: '图表右边距',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.chartGrid');
    },
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
    },
  },
  {
    name: 'componentStyle.visualMapPosition',
    title: '映射框位置',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          { title: '左侧', value: 'left' },
          { title: '中心', value: 'center' },
          { title: '右侧', value: 'right' },
        ],
        showSearch: true,
        allowClear: true,
      },
    },
  },
  {
    name: 'componentStyle.colorConfig',
    title: '颜色配置',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          { title: '渐变蓝', value: 'linear_blue' },
          { title: '渐变橙', value: 'linear_orange' },
          { title: '渐变黄', value: 'linear_yellow' },
          { title: '渐变绿', value: 'linear_green' },
          { title: '渐变紫', value: 'linear_purple' },
          { title: '渐变红', value: 'linear_red' },
        ],
        showSearch: true,
        allowClear: true,
      },
    },
  },
  {
    name: 'componentStyle.reverseColor',
    title: '颜色反转',
    defaultValue: false,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.dataFormatType',
    title: '数据格式',

    display: 'inline',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: DEFAULT_DATA_FORMAT_TYPE,
      },
    },
  },
];

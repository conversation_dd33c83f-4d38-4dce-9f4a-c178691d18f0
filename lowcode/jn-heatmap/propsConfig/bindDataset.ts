import { requestBjxDataset } from '../../service/chart/index';
import { get } from 'lodash';

export const bindDatasetItems = [
  {
    name: 'specificDataset.bindDataset',
    display: 'inline',
    title: '数据绑定',
    extraProps: {
      setValue: (target, value) => {
        if (value) {
          const extraGraphConfig = {
            requestView: 'GRAPH',
          };
          requestBjxDataset(value, extraGraphConfig).then((res) => {
            const chartMain = get(res, 'data.result.data.chartMain');

            target.parent.setPropValue('chartConfig.dimConfig.dataField', '');
            target.parent.setPropValue('chartConfig.ratioConfig.dataField', '');

            // 维度配置
            const rowDim = chartMain?.row?.meta?.find((item) => item.metaType === 'DIM');
            const columnDim = chartMain?.column?.meta?.find((item) => item.metaType === 'DIM');
            const dimOptions = [
              { title: rowDim?.title || rowDim?.originTitle, value: rowDim?.key || 'row' },
              {
                title: columnDim?.title || columnDim?.originTitle,
                value: columnDim?.key || 'column',
              },
            ];

            target.parent.setPropValue('storeConfig.dimOptions', dimOptions);
          });
        }

        return value;
      },
    },
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        mode: 'single',
        showSearch: true,
        filterType: 'List',
      },
    },
  },
];

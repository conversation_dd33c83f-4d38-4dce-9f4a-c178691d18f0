import { bindDatasetItems } from './propsConfig/bindDataset';
import { componentStyleItems } from './propsConfig/componentStyle';
import { chartConfigItems } from './propsConfig/chartConfig';
import { advancedItems } from './propsConfig/advanced';

const props = [
  // 绑定数据集
  {
    name: 'specificDataset',
    type: 'group',
    display: 'accordion',
    title: {
      label: '绑定数据集',
    },
    items: bindDatasetItems,
  },
  // 组件样式
  {
    name: 'componentStyle',
    type: 'group',
    display: 'accordion',
    title: {
      label: '组件样式',
    },
    items: componentStyleItems,
  },
  {
    name: 'chartConfig',
    type: 'group',
    display: 'accordion',
    title: {
      label: '图表配置',
    },
    items: chartConfigItems,
  },
  // 高级
  {
    name: 'advanced',
    type: 'field',
    display: 'accordion',
    title: {
      label: '高级',
    },
    setter: advancedItems,
  },
  // 默认样式
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'plain',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
      },
    ],
  },
];

export default props;

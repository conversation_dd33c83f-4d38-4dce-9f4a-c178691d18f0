import props from './props';

export const JNDrillingMeta = {
  componentName: 'JNDrillingContainer',
  title: '钻取',
  screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b032.png',
  icon: '',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '布局容器类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNDrillingContainer',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
  },
  snippets: [
    {
      screenshot: 'https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/icons/b032.png',
      title: '钻取',
      schema: {
        componentName: 'JNDrillingContainer',
        props: {
          dataList: [
            {
              title: '默认标题1',
              content: {
                type: 'JSSlot',
              },
            },
            {
              title: '默认标题2',
              content: {
                type: 'JSSlot',
              },
            },
            {
              title: '默认标题3',
              content: {
                type: 'JSSlot',
              },
            },
            {
              title: '默认标题4',
              content: {
                type: 'JSSlot',
              },
            },
          ],
        },
      },
    },
  ],
};

export default JNDrillingMeta;

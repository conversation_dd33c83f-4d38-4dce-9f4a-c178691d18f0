export default [
    {
        name: 'dataList',
        title: '钻取列',
        display: 'accordion',
        setter: {
            componentName: 'ArraySetter',
            props: {
                itemSetter: {
                    componentName: 'ObjectSetter',
                    props: {
                        config: {
                            items: [
                                {
                                    name: 'title',
                                    title: '名称',
                                    isRequired: true,
                                    setter: 'StringSetter',
                                },
                            ],
                        },
                    },
                    initialValue: () => {
                        return {
                            title: '默认名称',
                            content: {
                                type: 'JSSlot'
                            }
                        };
                    },
                },
            },
        },
    },
    {
        name: "style",
        title: "行内样式",
        display: 'accordion',
        setter: 'StyleSetter',
    },
]
import props from './props';

export const JNIframeBoxMeta = {
  componentName: 'JNIframeBox',
  title: 'iframe容器',
  docUrl: '',
  icon: '',
  devMode: 'procode',
  group: 'MelGeek组件',
  category: '布局容器类',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNIframeBox',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },

  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    supports: {
      events: ['onSave', 'onRemove'],
    },
  },
  snippets: [
    {
      title: 'iframe容器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b027.png',
      schema: {
        componentName: 'JNIframeBox',
        props: {},
      },
    },
  ],
};

export default JNIframeBoxMeta;

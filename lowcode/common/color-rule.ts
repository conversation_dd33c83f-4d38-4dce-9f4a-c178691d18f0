export const colorRuleCommon = () => {
  return {
    type: 'field',
    name: `colorRules`,
    title: '自定义规则',
    display: 'accordion',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'rule',
                  title: '规则',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: {
                      options: [
                        {
                          title: '大于',
                          value: '>',
                        },
                        {
                          title: '小于',
                          value: '<',
                        },
                        {
                          title: '等于',
                          value: '=',
                        },
                        {
                          title: '大于且等于',
                          value: '>=',
                        },
                        {
                          title: '小于且等于',
                          value: '<=',
                        },
                      ],
                    },
                  },
                },
                {
                  name: 'value',
                  title: '数值',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'NumberSetter',
                    props: {
                      precision: 3,
                    },
                  },
                },
                {
                  name: 'color',
                  title: '颜色',
                  display: 'inline',
                  setter: 'ColorSetter',
                },
              ],
            },
          },
          initialValue: () => {
            return {
              rule: '',
            };
          },
        },
      },
    },
    condition: (target) => {
      const numberColorState = target.parent.getPropValue('numberColorState');
      return numberColorState;
    },
  };
};

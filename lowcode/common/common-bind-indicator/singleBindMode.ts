export const singleBindMode = [
  {
    name: 'tooltip',
    title: '指标释义',
    defaultValue: false,
    setter: 'BoolSetter',
  },
  {
    name: 'tooltipMode',
    title: '指标释义模式',
    display: 'inline',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      return isShow;
    },
    setter: [
      {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              label: '自定义',
              value: 'custom',
            },
            {
              label: '指标绑定',
              value: 'bindIndicator',
            },
          ],
          showSearch: true,
          allowClear: true,
        },
      },
    ],
  },
  {
    name: 'tooltipBind',
    title: '绑定指标',
    display: 'inline',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'bindIndicator'
      return isShow && modeCorrect;
    },
    extraProps: {
      setValue: (target, val) => {
        const { jnIndicatorList } = window;
        jnIndicatorList &&
          jnIndicatorList?.forEach((item) => {
            if (item.value === val) {
              target.parent.setPropValue('title', item.label);
            }
          });
      },
    },
    setter: 'JNDicatorSetter',
  },
  {
    name: 'tooltipType',
    title: '自定义释义类型',
    defaultValue: 'simpleText',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'custom'
      return isShow && modeCorrect;
    },
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            title: '短文本释义',
            value: 'simpleText',
          },
          {
            title: '长文本释义',
            value: 'longText',
          },
          {
            title: '文档释义',
            value: 'doc',
          },
        ],
      },
    },
  },
  {
    name: 'tooltipTitle',
    title: '自定义释义标题',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'custom';
      const tooltipType = target.parent.getPropValue('tooltipType');
      const typePermit = tooltipType === 'longText' || tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'StringSetter',
  },
  {
    name: 'tooltipContent',
    title: '自定义释义内容',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'custom';
      const tooltipType = target.parent.getPropValue('tooltipType');
      const typePermit = tooltipType === 'simpleText' || tooltipType === 'longText';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'TextAreaSetter',
  },
  {
    name: 'tooltipDocIcon',
    title: '自定义释义文档图标',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'custom';
      const tooltipType = target.parent.getPropValue('tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'IconFontSetter',
  },
  {
    name: 'tooltipDocUrl',
    title: '自定义释义文档链接',
    condition: (target) => {
      const isShow = !!target.parent.getPropValue('tooltip');
      const modeCorrect = target.parent.getPropValue('tooltipMode') === 'custom';
      const tooltipType = target.parent.getPropValue('tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'TextAreaSetter',
  },
];

export const componentStyleMode = [
  {
    name: 'componentStyle.tooltip',
    title: '指标释义',
    defaultValue: false,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.tooltipMode',
    title: '指标释义模式',
    display: 'inline',
    defaultValue: 'bindIndicator',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.tooltip');
    },
    setter: [
      {
        componentName: 'SelectSetter',
        props: {
          options: [
            {
              label: '绑定指标平台',
              value: 'bindIndicator',
            },
            {
              label: '自定义',
              value: 'custom',
            },
          ],
          showSearch: true,
          allowClear: true,
        },
      },
    ],
  },
  {
    name: 'componentStyle.tooltipBind',
    title: '绑定指标',
    display: 'inline',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect =
        target.getProps().getPropValue('componentStyle.tooltipMode') === 'bindIndicator';
      return isShow && modeCorrect;
    },
    extraProps: {
      setValue: (target, val) => {
        const { jnIndicatorList } = window;
        jnIndicatorList &&
          jnIndicatorList?.forEach((item) => {
            if (item.value === val) {
              target.parent.setPropValue('componentStyle.title', item.label);
            }
          });
      },
    },
    setter: 'JNDicatorSetter',
  },
  {
    name: 'componentStyle.tooltipType',
    title: '自定义释义类型',
    defaultValue: 'simpleText',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect = target.getProps().getPropValue('componentStyle.tooltipMode') === 'custom';
      return isShow && modeCorrect;
    },
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            title: '短文本释义',
            value: 'simpleText',
          },
          {
            title: '长文本释义',
            value: 'longText',
          },
          {
            title: '文档释义',
            value: 'doc',
          },
        ],
      },
    },
  },
  {
    name: 'componentStyle.tooltipTitle',
    title: '自定义释义标题',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect = target.getProps().getPropValue('componentStyle.tooltipMode') === 'custom';
      const tooltipType = target.getProps().getPropValue('componentStyle.tooltipType');
      const typePermit = tooltipType === 'longText' || tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'StringSetter',
  },
  {
    name: 'componentStyle.tooltipContent',
    title: '自定义释义内容',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect = target.getProps().getPropValue('componentStyle.tooltipMode') === 'custom';
      const tooltipType = target.getProps().getPropValue('componentStyle.tooltipType');
      const typePermit = tooltipType === 'simpleText' || tooltipType === 'longText';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'TextAreaSetter',
  },
  {
    name: 'componentStyle.tooltipDocIcon',
    title: '自定义释义文档图标',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect = target.getProps().getPropValue('componentStyle.tooltipMode') === 'custom';
      const tooltipType = target.getProps().getPropValue('componentStyle.tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'IconFontSetter',
  },
  {
    name: 'componentStyle.tooltipDocUrl',
    title: '自定义释义文档链接',
    condition: (target) => {
      const isShow = !!target.getProps().getPropValue('componentStyle.tooltip');
      const modeCorrect = target.getProps().getPropValue('componentStyle.tooltipMode') === 'custom';
      const tooltipType = target.getProps().getPropValue('componentStyle.tooltipType');
      const typePermit = tooltipType === 'doc';

      return isShow && modeCorrect && typePermit;
    },
    setter: 'TextAreaSetter',
  },
];

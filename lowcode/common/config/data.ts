// TODO 下拉占位数据

export const ALL_CARD_SETTINGS = [
  {
    label: '经营驾驶舱-整体概览-销售金额混合指标卡',
    value: 'mixCardA-11',
  },
  {
    label: '经营驾驶舱-整体概览-付款毛利率混合指标卡',
    value: 'mixCardB',
  },
  {
    label: '经营驾驶舱-整体概览-初上市价折扣率指标卡',
    value: 'mixCardC',
  },
  {
    label: '经营驾驶舱-整体概览-客单价指标卡',
    value: 'mixCardD',
  },
  {
    label: '经营驾驶舱-整体概览-退款率(追溯)指标卡',
    value: 'mixCardE',
  },
  {
    label: '经营驾驶舱-整体概览-30天可用成本周转指标卡',
    value: 'mixCardF',
  },
  {
    label: '经营驾驶舱-整体概览-会员GMV贡献率',
    value: 'mixCardG',
  },
  {
    label: '经营驾驶舱-整体概览-差评率',
    value: 'mixCardH',
  },
  {
    label: '经营驾驶舱-渠道表现-电商整体',
    value: 'channelCardA',
  },
  {
    label: '经营驾驶舱-渠道表现-零售整体',
    value: 'channelCardB',
  },
  {
    label: '经营驾驶舱-商品经营-品类',
    value: 'productCardA',
  },
  {
    label: '经营驾驶舱-商品经营-单品',
    value: 'productCardB',
  },
];

export const bindDatasetItems = [
  {
    name: 'specificDataset.bindDataset',
    title: '所属板块',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: ALL_CARD_SETTINGS,
        showSearch: true,
      },
    },
  },
];



import { ALL_CARD_SETTINGS } from "./config/data";

export default [
  {
    name: 'componentStyle.showMoreText',
    title: '跳转',
    condition: (target) => {
      return target.getProps().getPropValue('componentStyle.version') !== 'compact';
    },
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.linkTitle',
    title: '跳转标题',
    defaultValue: '更多分析',
    setter: 'StringSetter',
    condition: (target) => {
      return target.getProps().getPropValue('componentStyle.showMoreText') || false;
    },
  },
  {
    name: 'componentStyle.moreActionType',
    title: '跳转展示',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          { title: '链接', value: 'more_link' },
          { title: '弹窗', value: 'more_modal' },
        ],
      },
    },
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.showMoreText');
    },
  },
  {
    name: 'componentStyle.locationType',
    title: '跳转方式',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          { title: '新窗口', value: 'target_blank' },
          { title: '当前窗口', value: 'target_self' },
        ],
      },
    },
    condition: (target) => {
      return (
        !!target.getProps().getPropValue('componentStyle.showMoreText') &&
        !!target.getProps().getPropValue('componentStyle.moreActionType')
      );
    },
  },
  {
    name: 'componentStyle.locationTarget',
    title: '跳转地址',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          // { title: '内部页面', value: 'inner_page' },
          { title: '外部链接', value: 'outer_link' },
        ],
      },
    },
    condition: (target) => {
      return (
        !!target.getProps().getPropValue('componentStyle.showMoreText') &&
        !!target.getProps().getPropValue('componentStyle.moreActionType') &&
        !!target.getProps().getPropValue('componentStyle.locationType')
      );
    },
  },
  {
    name: 'componentStyle.innerPageSetting',
    title: '内部锚点',
    setter: {
      componentName: 'SelectSetter',
      props: {
        showSearch: true,
        options: ALL_CARD_SETTINGS,
      },
    },
    condition: (target) => {
      return (
        !!target.getProps().getPropValue('componentStyle.showMoreText') &&
        !!target.getProps().getPropValue('componentStyle.moreActionType') &&
        !!target.getProps().getPropValue('componentStyle.locationType') &&
        target.getProps().getPropValue('componentStyle.locationTarget') === 'inner_page'
      );
    },
  },
  {
    name: 'componentStyle.outerLink',
    title: '外部链接',
    setter: 'StringSetter',
    condition: (target) => {
      return (
        !!target.getProps().getPropValue('componentStyle.showMoreText') &&
        !!target.getProps().getPropValue('componentStyle.moreActionType') &&
        !!target.getProps().getPropValue('componentStyle.locationType') &&
        target.getProps().getPropValue('componentStyle.locationTarget') === 'outer_link'
      );
    },

  }]

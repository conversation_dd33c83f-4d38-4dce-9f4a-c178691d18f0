import { setGlobalTimeFilterRange } from '../../utils/config';

export default {
    type: 'field',
    name: 'effectRanges',
    title: '影响范围',
    extraProps: {
        display: 'accordion',
        setValue: (target, value) => {
            const selectorType = target.parent.getPropValue('selectorType');
            const filterId = target.parent.getPropValue('filterId');
            const effectRanges = target.parent.getPropValue('effectRanges');
            const defalutKey = target.parent.getPropValue('defalutKey') || target.parent.getPropValue('defaultKey');
            if (selectorType === 'time') {
                setGlobalTimeFilterRange({
                    key: 'BT',
                    value: defalutKey,
                    filterId,
                    effectRanges,
                })
            }
        },
    },
    setter: {
        componentName: 'ArraySetter',
        props: {
            itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'type',
                                title: '类型',
                                isRequired: true,
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                label: '数据集',
                                                value: 'data',
                                            },
                                            {
                                                label: '筛选器',
                                                value: 'filter',
                                            },
                                            {
                                              label: '文本日期筛选器',
                                              value: 'data2Text',
                                            }
                                        ]
                                    }
                                }
                            },
                            {
                                name: 'id',
                                title: 'id',
                                isRequired: true,
                                setter: 'StringSetter',
                            },
                            {
                                name: 'value',
                                title: '取值',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                label: '开始',
                                                value: 'start',
                                            },
                                            {
                                                label: '结束',
                                                value: 'end',
                                            }
                                        ]
                                    }
                                },
                                condition: (target) => {
                                    return target.parent.parent.parent.getPropValue('selectorType') === 'time' && target.parent.getPropValue('type') === 'filter';
                                }
                            },
                            {
                                name: 'filterType',
                                title: '筛选器类型',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                label: '等于',
                                                value: 'EQ',
                                            },
                                            {
                                                label: '不等于',
                                                value: 'NE',
                                            },
                                            {
                                                label: '大于',
                                                value: 'GT',
                                            },
                                            {
                                                label: '大于等于',
                                                value: 'GE',
                                            },
                                            {
                                                label: '小于',
                                                value: 'LT',
                                            },
                                            {
                                                label: '小于等于',
                                                value: 'LE',
                                            },
                                        ]
                                    }
                                },
                                condition: (target) => {
                                    return target.parent.parent.parent.getPropValue('selectorType') === 'time' && target.parent.getPropValue('type') === 'filter';
                                }
                            }
                        ]
                    }
                }
            }
        }
    }
}

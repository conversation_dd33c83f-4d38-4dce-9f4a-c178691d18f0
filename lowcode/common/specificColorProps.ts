export const specificColorProps = [
  {
    name: 'valueColor',
    title: '数值颜色',
    condition: (target) => {
      const result = target.parent.getPropValue('showMode');
      return result !== 'progress';
    },
    setter: 'BoolSetter',
  },
  {
    name: 'colorType',
    title: '自定义颜色类型',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          {
            value: 'staticState',
            title: '静态',
          },
          {
            value: 'dynamic',
            title: '动态',
          },
        ],
      },
    },
    extraProps: {
      setValue: (target, value) => {
        const colorState = target.parent.getPropValue('valueColor');
        if (value === 'dynamic' && colorState) {
          const colorRules = [
            {
              rule: '<',
              value: 0.9,
              color: '#F65B5B',
            },
            {
              rule: '>=',
              value: 0.9,
              color: '#FB8437',
            },
            {
              rule: '>=',
              value: 1,
              color: '#59C252',
            },
          ];
          target.parent.setPropValue('colorRules', colorRules);
        }
      },
    },
    condition: (target) => {
      return target.parent.getPropValue('valueColor') === true;
    },
  },
  {
    name: 'color',
    title: '自定义静态颜色',
    setter: 'ColorSetter',
    condition: (target) => {
      return (
        target.parent.getPropValue('valueColor') === true &&
        target.parent.getPropValue('colorType') === 'staticState'
      );
    },
  },
  {
    type: 'field',
    name: 'colorRules',
    title: '自定义规则',
    display: 'accordion',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'rule',
                  title: '规则',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: {
                      options: [
                        {
                          title: '大于',
                          value: '>',
                        },
                        {
                          title: '小于',
                          value: '<',
                        },
                        {
                          title: '等于',
                          value: '=',
                        },
                        {
                          title: '大于且等于',
                          value: '>=',
                        },
                        {
                          title: '小于且等于',
                          value: '<=',
                        },
                      ],
                    },
                  },
                },
                {
                  name: 'value',
                  title: '数值',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'NumberSetter',
                    props: {
                      precision: 3,
                    },
                  },
                },
                {
                  name: 'color',
                  title: '颜色',
                  display: 'inline',
                  setter: 'ColorSetter',
                },
              ],
            },
          },
          initialValue: () => {
            return {
              rule: '',
            };
          },
        },
      },
    },
    condition: (target) => {
      return (
        target.parent.getPropValue('valueColor') === true &&
        target.parent.getPropValue('colorType') === 'dynamic'
      );
    },
  },
];

import { colors } from "./color.config";

export const componentStyle = ({
  verticalMergeControl = false, // 纵向合并  默认不显示
  hasPagination = true, // 是否显示分页  默认显示
}) => {
  return {
    type: 'select',
    name: '',
    title: '组件样式',
    display: 'accordion',
    setter: (field: any, target: any) => {
      return {
        componentName: 'ObjectSetter',
        display: 'inline',
        props: {
          config: {
            items: [
              {
                name: 'tableTitle',
                title: '表格标题',
                setter: 'StringSetter',
              },
              {
                name: 'tableIcon',
                title: '标题图标',
                setter: 'IconFontSetter',
              },
              {
                name: 'tableContent',
                title: '左侧自定义容器',
                setter: 'SlotSetter',
              },
              {
                name: 'border',
                title: '边框',
                setter: {
                  componentName: 'SelectSetter',
                  initialValue: 1,
                  props: {
                    options: [
                      {
                        title: '无边框',
                        value: 1,
                      },
                      {
                        title: '有边框',
                        value: 2,
                      },
                    ],
                  },
                },
              },
              {
                name: 'tableBackground',
                title: '背景颜色',
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: colors,
                  },
                },
              },
              // 已废弃
              // {
              //     name: 'compactState',
              //     title: '紧凑表格',
              //     setter: 'BoolSetter',
              // },
              {
                name: 'componentStyle.layoutControl',
                title: '布局控制',
                setter: {
                  componentName: 'BoolSetter',
                  initialValue: false,
                },
              },
              {
                name: 'componentStyle.defaultLayout',
                title: '默认布局',
                setter: {
                  componentName: 'CustomRadioGroupSetter',
                  props: {
                    dataSource: [
                      { label: '紧凑型', value: 'compact' },
                      { label: '宽松型', value: 'relaxed' },
                    ],
                  },
                  initialValue: 'compact',
                },
              },
              {
                name: 'zebraCrossing',
                title: '斑马线',
                initialValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'zebraCrossingType',
                title: '斑马线类型',
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      { label: '行斑马线', value: 'row' },
                      { label: '列斑马线', value: 'cloumn' },
                    ],
                  },
                  initialValue: 'row',
                },
                condition: (target: { parent: { getPropValue: (arg0: string) => any } }) => {
                  return target.parent.getPropValue('zebraCrossing') || false;
                },
              },
              {
                name: 'startIndex',
                title: '起始列',
                setter: 'NumberSetter',
                condition: (target: {
                  parent: { getPropValue: (arg0: string) => string | boolean };
                }) => {
                  return (
                    target.parent.getPropValue('zebraCrossing') === true &&
                    target.parent.getPropValue('zebraCrossingType') === 'cloumn'
                  );
                },
              },
              {
                name: 'renderIndex',
                title: '渲染列',
                defaultValue: 1,
                setter: 'NumberSetter',
                condition: (target: {
                  parent: { getPropValue: (arg0: string) => string | boolean };
                }) => {
                  return (
                    target.parent.getPropValue('zebraCrossing') === true &&
                    target.parent.getPropValue('zebraCrossingType') === 'cloumn'
                  );
                },
              },
              {
                name: 'defaultExpandAllRows',
                title: '展开行',
                initialValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'defaultExpandLevel',
                title: '默认层级',
                setter: 'NumberSetter',
              },
              {
                name: 'fixedHeader',
                title: '固定表头',
                initialValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'offsetHeader',
                title: '浮动表头',
                setter: 'NumberSetter',
              },
              {
                name: 'tableHeight',
                title: '列表高度',
                setter: 'NumberSetter',
                defaultValue: 500,
                condition: (target: { parent: { getPropValue: (arg0: string) => any } }) => {
                  return target.parent.getPropValue('fixedHeader') || false;
                },
              },
              {
                name: 'hasPagination',
                title: '分页显示',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target:any) => {
                  return hasPagination
                }
              },
              {
                name: 'pageSize',
                title: '默认页行数',
                setter: 'NumberSetter',
                defaultValue: 10,
                condition: (target: { parent: { getPropValue: (arg0: string) => any } }) => {
                  return hasPagination &&  (target.parent.getPropValue('hasPagination') || false);
                },
              },
              {
                name: 'tooltipConfig.tooltipControl',
                title: '说明开关',
                setter: 'BoolSetter',
              },
              {
                name: 'tooltipConfig.tooltipType',
                title: '说明类型',
                defaultValue: 'simpleText',
                condition: (target: {
                  getProps: () => {
                    (): any;
                    new (): any;
                    getPropValue: { (arg0: string): any; new (): any };
                  };
                }) => {
                  return !!target.getProps().getPropValue('tooltipConfig.tooltipControl');
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      {
                        title: '短文本释义',
                        value: 'simpleText',
                      },
                      {
                        title: '长文本释义',
                        value: 'longText',
                      },
                      {
                        title: '文档释义',
                        value: 'doc',
                      },
                    ],
                  },
                },
              },
              {
                name: 'tooltipConfig.tooltipTitle',
                title: '说明标题',
                condition: (target: {
                  getProps: () => {
                    (): any;
                    new (): any;
                    getPropValue: { (arg0: string): any; new (): any };
                  };
                }) => {
                  const isShow = !!target.getProps().getPropValue('tooltipConfig.tooltipControl');
                  const tooltipType = target.getProps().getPropValue('tooltipConfig.tooltipType');
                  const typePermit = tooltipType === 'longText' || tooltipType === 'doc';

                  return isShow && typePermit;
                },
                setter: 'StringSetter',
              },
              {
                name: 'tooltipConfig.tooltip',
                title: '说明内容',
                condition: (target: {
                  getProps: () => {
                    (): any;
                    new (): any;
                    getPropValue: { (arg0: string): any; new (): any };
                  };
                }) => {
                  const isShow = !!target.getProps().getPropValue('tooltipConfig.tooltipControl');
                  const tooltipType = target.getProps().getPropValue('tooltipConfig.tooltipType');
                  const typePermit = tooltipType === 'simpleText' || tooltipType === 'longText';

                  return isShow && typePermit;
                },
                setter: 'TextAreaSetter',
              },
              {
                name: 'tooltipConfig.tooltipDocIcon',
                title: '说明文档图标',
                condition: (target: {
                  getProps: () => {
                    (): any;
                    new (): any;
                    getPropValue: { (arg0: string): any; new (): any };
                  };
                }) => {
                  const isShow = !!target.getProps().getPropValue('tooltipConfig.tooltipControl');
                  const tooltipType = target.getProps().getPropValue('tooltipConfig.tooltipType');
                  const typePermit = tooltipType === 'doc';

                  return isShow && typePermit;
                },
                setter: 'IconFontSetter',
              },
              {
                name: 'tooltipConfig.tooltipDocUrl',
                title: '说明文档链接',
                condition: (target: {
                  getProps: () => {
                    (): any;
                    new (): any;
                    getPropValue: { (arg0: string): any; new (): any };
                  };
                }) => {
                  const isShow = !!target.getProps().getPropValue('tooltipConfig.tooltipControl');
                  const tooltipType = target.getProps().getPropValue('tooltipConfig.tooltipType');
                  const typePermit = tooltipType === 'doc';

                  return isShow && typePermit;
                },
                setter: 'TextAreaSetter',
              },
              {
                name: 'tooltipConfig.verticalMergeControl',
                title: '纵向合并',
                setter: 'BoolSetter',
                condition: () => {
                  return verticalMergeControl;
                },
              },
            ],
          },
        },
      };
    },
  };
};
export const positioning = {
  name: '',
  title: '自定义表头',
  display: 'accordion',
  setter: (field: any, target: any) => {
    return {
      componentName: 'ObjectSetter',
      display: 'inline',
      props: {
        config: {
          items: [
            {
              name: 'indicatorConfiguration',
              title: '是否显示',
              initialValue: false,
              setter: 'BoolSetter',
            },
            {
              name: 'showType',
              title: '展示方式',
              initialValue: false,
              defaultValue: 'dropDownOption',
              setter: {
                componentName: 'SelectSetter',
                initialValue: 'dropDownOption',
                props: {
                  options: [
                    { value: 'tileButton', title: '平铺按钮' },
                    { value: 'tileSelectBox', title: '平铺选框' },
                    { value: 'dropDownOption', title: '下拉选项' },
                  ],
                },
              },
              condition: (target: { parent: { getPropValue: (arg0: string) => any } }) => {
                return target.parent.getPropValue('indicatorConfiguration') || false;
              },
            },
            {
              name: 'positioning.title',
              title: '自定义标题',
              defaultValue: '配置表头',
              setter: 'StringSetter',
              condition: (target: { parent: { getPropValue: (arg0: string) => any } }) => {
                return target.parent.getPropValue('indicatorConfiguration') || false;
              },
            },
          ],
        },
      },
    };
  },
};

export default {
    type: 'field',
    name: 'popupConfigList',
    title: '浮窗配置',
    display: 'accordion',
    setter: {
        componentName: 'ArraySetter',
        props: {
            itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'name',
                                title: '名称',
                                display: 'inline',
                                isRequired: true,
                                setter: 'StringSetter',
                            },
                            {
                                name: 'popupConfig',
                                title: '浮窗配置',
                                isRequired: true,
                                setter: 'PopupBoxSetter',
                            },
                            {
                              name: 'trigger',
                              title: '浮窗交互',
                              setter: {
                                componentName: 'SelectSetter',
                                props: {
                                  options: [
                                    { value: 'hover', title: '悬停' },
                                    { value: 'click', title: '点击' },

                                  ],
                                },
                              },
                            },
                            {
                              name: 'top',
                              title: '距离顶部',
                              setter: 'NumberSetter',
                            },
                            {
                              name: 'moreLink',
                              title: '更多跳转',
                              setter: 'BoolSetter',
                              initialValue: false,
                            },
                            {
                              name: 'moreLinkText',
                              title: '跳转文案',
                              setter: 'StringSetter',
                              condition: (target) => {
                                const moreLink = target.parent.getPropValue('moreLink');
                                return moreLink;
                              },
                            },

                            {
                              name: 'linkConfig',
                              title: '跳转配置',
                              setter: 'LinkBoxSetter',
                              condition: (target) => {
                                const moreLink = target.parent.getPropValue('moreLink');
                                return moreLink;
                              },
                            }
                        ],
                    },
                },
                initialValue: () => {
                    return {
                        name: '浮窗',
                        moreLink: false,
                        moreLinkText: '更多分析',
                        trigger: "hover",
                        top: 10,
                    };
                },
            },
        },
    },
    condition: (target) => {
        const hasPopup = target.parent.getPropValue('hasPopup');
        return !!hasPopup;
    },
}

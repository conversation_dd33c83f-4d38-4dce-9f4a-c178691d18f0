import is from '@sindresorhus/is';
import _ from 'lodash';

export const uuid = () => Math.random().toString(36).substr(-6);

export const PRO_TABLE_COLUMN_MOCK_VALUES = {
  text: '这是一个文本',
  number: 1234561.231,
  money: 123213.1232,
  date: '2021-08-18 20:52:33',
  phone: '+86 ***********',
  currency: 'CNY',
  ou: '34',
  percent: 0.64,
  progress: 0.64,
  link: '这是链接',
  tag: '成功',
  textTag: '进行中',
  files: [],
  bankCard: '****************',
  employee: {
    img: 'https://work.alibaba-inc.com/photo/256512.40x40.jpg',
    staff_id: '256512',
    nickname: '乔勇',
    realname: '石强',
  },
};
export const mockProTableCell = (formatType) => PRO_TABLE_COLUMN_MOCK_VALUES[formatType];

export const mockId = () => `id-${uuid()}`;
export const columnMockValueKey = (formatType) => `_mock_value_${formatType}`;
export const mockProTableRow = (columns: any[]) => {
  return columns
    ?.filter?.((vo) => vo.formatType && vo.dataIndex)
    ?.reduce?.((p, column) => {
      const { formatType, dataIndex } = column;
      const mockValue = column[columnMockValueKey(formatType)];
      p[dataIndex] = is.nullOrUndefined(mockValue) ? mockProTableCell(formatType) : mockValue;
      return p;
    }, {});
};

export const getDataSourceItemSetter = (formatType) => {
  let setter;
  switch (formatType) {
    case 'text':
    case 'phone':
    case 'link':
    case 'dialog':
      setter = 'StringSetter';
      break;
    case 'number':
    case 'money':
    case 'percent':
    case 'currency':
    case 'progress':
      setter = 'NumberSetter';
      break;
    case 'date':
      setter = 'DateSetter';
      break;
    default:
      setter = 'StringSetter';
  }
  return setter;
};

export const filterColumns = (list: any) => {
  let options:{label: string, value: string | number}[] = [];
  if (list?.children?.length) {
    list.children.map(item => {
      options.push({
        label: item?.title,
        value: item?.dataIndex ||list?.key,
      })
    })
  } else if (list?.assistants?.length) {
    list.assistants.map(item => {
      options.push({
        label: item?.title,
        value: item?.dataIndex ||list?.key,
      })
    })
  }
  options.push({
    label: list?.title,
    value: list?.dataIndex ||list?.key,
  })
  return options;
}
export type ChildType<T> = {
  children?: T[];
  assistants?: T[];
};

// 递归获取所有的columns字段
export const getAllColumns = <T extends ChildType<T>>(list: T[],backList: T[] = []): T[] => {

  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item.children?.length) {
      getAllColumns(item.children, backList)

    } else if (item.assistants?.length) {
      getAllColumns(item.assistants, backList)
    }
    backList.push(item);
  }

  return backList;
};


export const diffComparison = (baseArray: any[], comparisonArray: any[], backData:any) =>{

  for(let i = baseArray.length -1; i >= 0; i--) {
    const oldColumn = baseArray[i];

    // 对数组进行对比， 如果找到对应的值则获取出来并从原数组删除
    for(let j = 0; j < comparisonArray.length; j++){
      if(oldColumn.dataIndex === comparisonArray[j].key){
        const findColumn =  comparisonArray.splice(j, 1)[0];
        backData.unshift({fieldMeta: _.get(findColumn, 'fieldMeta'), ...oldColumn, assistants:[], children:[]});

        // 如果是主副指标
        if(findColumn?.assistants?.length){
          // backData[0].assistants = []
          diffComparison(oldColumn.assistants ||[], findColumn?.assistants || [],backData[0].assistants )
        }else{
          backData[0].assistants = []
        }
        // 如果是组黑数据
        if(findColumn?.children?.length){
          // backData[0].children = []
          diffComparison(oldColumn.children || [], findColumn?.children || [],backData[0].children )
        }else{
          backData[0].children = []
        }
        break
      }
    }
    if(i=== 0 && comparisonArray) {
      // console.log('==========================', comparisonArray)
      backData.push(...comparisonArray)
    }

}
}

export const setFixed = (children, value) => {
  if (children && children?.length) {
    children.forEach(item => {
      item.fixed = value;
    });
    children.map(_item => {
      if (_item?.children && _item?.children?.length) {
        setFixed(_item?.children, value);
      }
    })
  }

  return children;
}

// 对数据进行过滤
export const fitlerDynamicGroup =(column:any) => {
  const backColumns:any[] = [] // 返回的可以配置的类
  const loopColumns:any[] = [] // 无法配置的循环列
  let dataList:any[] = [] // 数据值根据数据配置
  // 返回数据
  for (let i = 0; i < column.length; i++) {
    const item = column[i];
    if(item.mergeType !== "dynamic") {

      backColumns.push(item)
      // 数据深度寻找数据值
    }else{
      loopColumns.push(item)
    }
  }
  return [backColumns, loopColumns, dataList]
}

export const findColumnsData = (column, dataList,level) => {
  // let back:any[] = []
  if(column?.children && column?.children?.length > 0) {
    if(column?.children[0] && column?.children[0]?.children && column?.children[0]?.children?.length > 0) {
      findColumnsData(column.children[0], dataList, level++)
    }else if(!dataList.length ) {
      if(level === 0) {
        dataList.push({
          // title: column.children[0].title,
          type: 'text'
        })
      }else{
        dataList.push(...column.children.map((item) => ({
          // title: item.title,
          type: 'text'
        })))
      }

    }
  }
}

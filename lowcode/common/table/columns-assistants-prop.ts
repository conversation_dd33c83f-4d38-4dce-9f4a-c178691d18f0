import colorRules from './colorRules-prop';
import textColorRules from './textRules-prop';
import { filterColumns, getAllColumns } from './utils';
import _ from 'lodash';
import dateColorRules from './date-color-rules';
import { flexibleRouteWithoutLinkProps } from './flexible-route-props';
export default {
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'title',
              title: '标题',
              isRequired: true,
              setter: 'StringSetter',
            },
            {
              name: 'dataIndex',
              title: '数据字段',
              display: 'inline',
              extraProps: {
                setValue: (target, value) => {
                  const columns = target.getProps().getPropValue('columnsCopy');
                  const allColumns = getAllColumns(columns);
                  const selectColumn = _.find(allColumns, { dataIndex: `${value}` });

                  target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                },
              },
              setter: {
                componentName: 'SelectSetter',
                props: (target) => {
                  const columns = target.getProps().getPropValue('columnsCopy');

                  let option = [];
                  if (columns && columns.length) {
                    columns.map((item) => {
                      option.push(...filterColumns(item));
                    });
                  }

                  return {
                    options: option,
                    showSearch: true,
                  };
                },
              },
            },
            {
              name: 'warpWidth',
              title: '宽度',
              display: 'inline',
              setter: 'NumberSetter',
            },
            {
              name: 'align',
              title: '对齐方式',
              display: 'inline',
              initialValue: 'left',
              setter: {
                componentName: 'RadioGroupSetter',
                props: {
                  options: [
                    {
                      value: 'left',
                      title: '居左',
                    },
                    {
                      value: 'center',
                      title: '居中',
                    },
                    {
                      value: 'right',
                      title: '居右',
                    },
                  ],
                },
              },
            },
            {
              name: 'type',
              title: '数据类型',
              display: 'inline',
              initialValue: 'text',
              isRequired: true,
              extraProps: {
                setValue: (target, value) => {
                  switch (value) {
                    case 'number':
                      target.parent.setPropValue('align', 'right');
                      target.parent.setPropValue('textColorState', false);
                      break;
                    case 'text':
                      target.parent.setPropValue('numberColorState', false);
                      break;
                    case 'progress':
                      target.parent.setPropValue('warpWidth', 180);
                      break;
                    default:
                      break;
                  }
                },
              },
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: [
                    { value: 'text', title: '文本' },
                    { value: 'number', title: '数字' },
                    { value: 'progress', title: '进度条' },
                    { value: 'date', title: '日期' },
                  ],
                },
              },
            },
            {
              name: 'subTitle', // 只有主副指标有这个数据
              title: '副标题',
              display: 'inline',
              defaultValue: '',
              setter: 'StringSetter',
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                return formatType === 'merge';
              },
            },
            {
              name: 'arrow',
              title: '显示升降',
              display: 'inline',
              initialValue: false,
              setter: 'BoolSetter',
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                return formatType !== 'mergeChildren';
              },
            },
            {
              name: 'data',
              type: 'group',
              display: 'accordion',
              title: '对比配置',
              items: [
                {
                  name: 'contrastKey',
                  title: '对比字段',
                  display: 'inline',
                  extraProps: {
                    setValue: (target, value) => {
                      const columns = target.getProps().getPropValue('columnsCopy');
                      const selectColumn = _.find(columns, { dataIndex: `${value}` });
                      target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                    },
                  },
                  setter: {
                    componentName: 'SelectSetter',
                    props: (target) => {
                      const columns = target.getProps().getPropValue('columnsCopy');

                      let option = [];
                      if (columns && columns.length) {
                        columns.map((item) => {
                          option.push(...filterColumns(item));
                        });
                      }

                      return {
                        options: option,
                        showSearch: true,
                      };
                    },
                  },
                },
                {
                  name: 'largerValue',
                  title: '较大值颜色',
                  display: 'inline',
                  defaultValue: '#F65B5B',
                  setter: 'ColorSetter',
                },
                {
                  name: 'smallerValue',
                  title: '较小值颜色',
                  display: 'inline',
                  defaultValue: '#59C252',
                  setter: 'ColorSetter',
                },
              ],
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                const contrastState = target.parent.getPropValue('contrastState');
                return ['text', 'number'].includes(formatType) && contrastState === true;
              },
            },
            {
              name: 'numberColorState',
              title: '数字状态颜色',
              display: 'inline',
              initialValue: false,
              setter: 'BoolSetter',
              extraProps: {
                setValue: (target, value) => {
                  target.parent.setPropValue('numberColorState', value);
                  if (value) {
                    const colorRules = [
                      {
                        rule: '<',
                        value: 90,
                        color: '#F65B5B',
                      },
                      {
                        rule: '>=',
                        value: 90,
                        color: '#FB8437',
                      },
                      {
                        rule: '>=',
                        value: 100,
                        color: '#59C252',
                      },
                    ];
                    target.parent.setPropValue('colorRules', colorRules);
                  }
                },
              },
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                const contrastState = target.parent.getPropValue('contrastState');
                return ['number', 'progress'].includes(formatType) && !contrastState;
              },
            },
            colorRules,
            {
              name: 'textColorState',
              title: '文本状态颜色',
              display: 'inline',
              setter: 'BoolSetter',
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                const contrastState = target.parent.getPropValue('contrastState');
                return ['text'].includes(formatType) && !contrastState;
              },
            },
            textColorRules,
            dateColorRules,
            {
              name: 'emptyValueShow',
              title: '保留空值显示',
              display: 'inline',
              defaultValue: false,
              setter: 'BoolSetter',
            },
            {
              name: 'showEmptyValue',
              title: '图片隐藏空值',
              display: 'inline',
              defaultValue: false,
              setter: 'BoolSetter',
              condition: (target) => {
                const formatType = target.parent.getPropValue('type');
                return formatType === 'image';
              },
            },
            {
              name: 'defaultValue',
              title: '默认空值',
              display: 'inline',
              defaultValue: '-',
              setter: 'StringSetter',
              condition: (target) => {
                return !target.parent.getPropValue('emptyValueShow');
              },
            },
            // {
            //     name: 'assistants',
            //     title: '合并内容',
            //     display: 'block',
            //     condition: (target) => {
            //         const formatType = target.parent.getPropValue('type');
            //         return formatType === 'merge' || false;
            //     },
            //     setter: {
            //         componentName: 'ArraySetter',
            //         props: {
            //             itemSetter: {
            //                 componentName: 'ObjectSetter',
            //                 props: {
            //                     config: {
            //                         items: [
            //                             {
            //                                 name: 'title',
            //                                 title: '标题',
            //                                 isRequired: true,
            //                                 setter: 'StringSetter',
            //                             },
            //                             {
            //                                 name: 'dataIndex',
            //                                 title: '数据字段',
            //                                 display: 'inline',
            //                                 extraProps: {
            //                                     setValue: (target, value) => {
            //                                         const columns = target.getProps().getPropValue('columnsCopy');
            //                                         const selectColumn = _.find(columns, { dataIndex: `${value}` })
            //                                         target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
            //                                     }
            //                                 },
            //                                 setter: {
            //                                     componentName: 'SelectSetter',
            //                                     props: (target) => {
            //                                         const columns = target.getProps().getPropValue('columnsCopy');

            //                                         let option = [];
            //                                         if (columns && columns.length) {
            //                                             columns.map(item => {
            //                                                 option.push(...filterColumns(item));
            //                                             })
            //                                         }

            //                                         return {
            //                                             options: option,
            //                                             showSearch: true,
            //                                         }
            //                                     }
            //                                 },
            //                             },
            //                             {
            //                                 name: 'warpWidth',
            //                                 title: '宽度',
            //                                 display: 'inline',
            //                                 initialValue: 100,
            //                                 setter: 'NumberSetter',
            //                             },
            //                             {
            //                                 name: 'align',
            //                                 title: '对齐方式',
            //                                 display: 'inline',
            //                                 initialValue: 'left',
            //                                 setter: {
            //                                     componentName: 'RadioGroupSetter',
            //                                     props: {
            //                                         options: [
            //                                             {
            //                                                 value: 'left',
            //                                                 title: '居左',
            //                                             },
            //                                             {
            //                                                 value: 'center',
            //                                                 title: '居中',
            //                                             },
            //                                             {
            //                                                 value: 'right',
            //                                                 title: '居右',
            //                                             },
            //                                         ],
            //                                     },
            //                                 },
            //                             },
            //                             {
            //                                 name: 'type',
            //                                 title: '数据类型',
            //                                 display: 'inline',
            //                                 initialValue: 'text',
            //                                 isRequired: true,
            //                                 getValue: (target) => {
            //                                     const type = target.parent.getPropValue('type');
            //                                     if (type === 'merge' || type === 'mergeChildren') {
            //                                         target.parent.setPropValue('numberColorState', false);
            //                                     }
            //                                     switch (type) {
            //                                         case 'image':
            //                                             target.parent.setPropValue('warpWidth', 38);
            //                                             break;
            //                                         case 'progress':
            //                                             target.parent.setPropValue('warpWidth', 180);
            //                                             break;
            //                                         default:
            //                                             break;
            //                                     }
            //                                     return type;
            //                                 },

            //                                 setter: {
            //                                     componentName: 'SelectSetter',
            //                                     props: {
            //                                         options: [
            //                                             { value: 'text', title: '文本' },
            //                                             { value: 'number', title: '数字' },
            //                                             { value: 'progress', title: '进度条' },
            //                                         ],
            //                                     },
            //                                 },
            //                             },
            //                             {
            //                                 name: 'arrow',
            //                                 title: '显示升降',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                             },
            //                             {
            //                                 name: 'contrastState',
            //                                 title: '是否对比',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                                 condition: (target) => {
            //                                     const numberColorState = target.parent.getPropValue('numberColorState');
            //                                     return !numberColorState && true;
            //                                 },
            //                             },
            //                             {
            //                                 name: 'data',
            //                                 type: 'group',
            //                                 display: 'accordion',
            //                                 title: '对比配置',
            //                                 items: [
            //                                     {
            //                                         name: 'contrastKey',
            //                                         title: '对比字段',
            //                                         display: 'inline',
            //                                         extraProps: {
            //                                             setValue: (target, value) => {
            //                                                 const columns = target.getProps().getPropValue('columnsCopy');
            //                                                 const selectColumn = _.find(columns, { dataIndex: `${value}` })
            //                                                 target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
            //                                             }
            //                                         },
            //                                         setter: {
            //                                             componentName: 'SelectSetter',
            //                                             props: (target) => {
            //                                                 const columns = target.getProps().getPropValue('columnsCopy');

            //                                                 let option = [];
            //                                                 if (columns && columns.length) {
            //                                                     columns.map(item => {
            //                                                         option.push(...filterColumns(item));
            //                                                     })
            //                                                 }

            //                                                 return {
            //                                                     options: option,
            //                                                     showSearch: true,
            //                                                 }
            //                                             }
            //                                         },
            //                                     },
            //                                     {
            //                                         name: 'largerValue',
            //                                         title: '较大值颜色',
            //                                         display: 'inline',
            //                                         defaultValue: '#F65B5B',
            //                                         setter: 'ColorSetter',
            //                                     },
            //                                     {
            //                                         name: 'smallerValue',
            //                                         title: '较小值颜色',
            //                                         display: 'inline',
            //                                         defaultValue: '#59C252',
            //                                         setter: 'ColorSetter',
            //                                     },
            //                                 ],
            //                                 condition: (target) => {
            //                                     const formatType = target.parent.getPropValue('type');
            //                                     const contrastState = target.parent.getPropValue('contrastState');
            //                                     return ['text', 'number'].includes(formatType) && contrastState === true;
            //                                 },
            //                             },
            //                             {
            //                                 name: 'numberColorState',
            //                                 title: '数字状态颜色',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                                 extraProps: {
            //                                     setValue: (target, value) => {
            //                                         target.parent.setPropValue('numberColorState', value);
            //                                         if (value) {
            //                                             const colorRules = [
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 80,
            //                                                     color: '#FB8437',
            //                                                 },
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 95,
            //                                                     color: '#489CFF',
            //                                                 },
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 100,
            //                                                     color: '#59C252',
            //                                                 },
            //                                                 {
            //                                                     rule: '<',
            //                                                     value: 80,
            //                                                     color: '#F65B5B',
            //                                                 },
            //                                             ]
            //                                             target.parent.setPropValue('colorRules', colorRules);
            //                                         }
            //                                     },
            //                                 },
            //                                 condition: (target) => {
            //                                     const formatType = target.parent.getPropValue('type');
            //                                     const contrastState = target.parent.getPropValue('contrastState');
            //                                     return formatType !== 'merge' && formatType !== 'mergeChildren' && !contrastState;
            //                                 },
            //                             },
            //                             colorRules,
            //                             {
            //                                 name: 'emptyValueShow',
            //                                 title: '保留空值显示',
            //                                 display: 'inline',
            //                                 defaultValue: false,
            //                                 setter: 'BoolSetter',
            //                             },
            //                             {
            //                                 name: 'defaultValue',
            //                                 title: '默认空值',
            //                                 display: 'inline',
            //                                 defaultValue: '-',
            //                                 setter: 'StringSetter',
            //                                 condition: (target) => {
            //                                     return !target.parent.getPropValue('emptyValueShow');
            //                                 },
            //                             },
            //                         ]
            //                     }
            //                 },
            //                 initialValue: () => {
            //                     return {
            //                         title: '列标题',
            //                         type: 'text',
            //                         dataIndex: 'dataIndex',
            //                         assistants: [],
            //                         children: [],
            //                     };
            //                 },
            //             }
            //         },
            //     }
            // },
            // {
            //     name: 'children',
            //     title: '合并内容',
            //     display: 'block',
            //     condition: (target) => {
            //         const formatType = target.parent.getPropValue('type');
            //         return formatType === 'mergeChildren' || false;
            //     },
            //     setter: {
            //         componentName: 'ArraySetter',
            //         props: {
            //             itemSetter: {
            //                 componentName: 'ObjectSetter',
            //                 props: {
            //                     config: {
            //                         items: [
            //                             {
            //                                 name: 'title',
            //                                 title: '标题',
            //                                 isRequired: true,
            //                                 setter: 'StringSetter',
            //                             },
            //                             {
            //                                 name: 'dataIndex',
            //                                 title: '数据字段',
            //                                 display: 'inline',
            //                                 extraProps: {
            //                                     setValue: (target, value) => {
            //                                         const columns = target.getProps().getPropValue('columnsCopy');
            //                                         const selectColumn = _.find(columns, { dataIndex: `${value}` })
            //                                         target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
            //                                     }
            //                                 },
            //                                 setter: {
            //                                     componentName: 'SelectSetter',
            //                                     props: (target) => {
            //                                         const columns = target.getProps().getPropValue('columnsCopy');

            //                                         let option = [];
            //                                         if (columns && columns.length) {
            //                                             columns.map(item => {
            //                                                 option.push(...filterColumns(item));
            //                                             })
            //                                         }

            //                                         return {
            //                                             options: option,
            //                                             showSearch: true,
            //                                         }
            //                                     }
            //                                 },
            //                             },
            //                             {
            //                                 name: 'warpWidth',
            //                                 title: '宽度',
            //                                 display: 'inline',
            //                                 initialValue: 100,
            //                                 setter: 'NumberSetter',
            //                             },
            //                             {
            //                                 name: 'align',
            //                                 title: '对齐方式',
            //                                 display: 'inline',
            //                                 initialValue: 'left',
            //                                 setter: {
            //                                     componentName: 'RadioGroupSetter',
            //                                     props: {
            //                                         options: [
            //                                             {
            //                                                 value: 'left',
            //                                                 title: '居左',
            //                                             },
            //                                             {
            //                                                 value: 'center',
            //                                                 title: '居中',
            //                                             },
            //                                             {
            //                                                 value: 'right',
            //                                                 title: '居右',
            //                                             },
            //                                         ],
            //                                     },
            //                                 },
            //                             },
            //                             {
            //                                 name: 'type',
            //                                 title: '数据类型',
            //                                 display: 'inline',
            //                                 initialValue: 'text',
            //                                 isRequired: true,
            //                                 getValue: (target) => {
            //                                     const type = target.parent.getPropValue('type');
            //                                     if (type === 'merge' || type === 'mergeChildren') {
            //                                         target.parent.setPropValue('numberColorState', false);
            //                                     }
            //                                     switch (type) {
            //                                         case 'image':
            //                                             target.parent.setPropValue('warpWidth', 38);
            //                                             break;
            //                                         case 'progress':
            //                                             target.parent.setPropValue('warpWidth', 180);
            //                                             break;
            //                                         default:
            //                                             break;
            //                                     }
            //                                     return type;
            //                                 },

            //                                 setter: {
            //                                     componentName: 'SelectSetter',
            //                                     props: {
            //                                         options: [
            //                                             { value: 'text', title: '文本' },
            //                                             { value: 'number', title: '数字' },
            //                                             { value: 'merge', title: '主副合并' },
            //                                             { value: 'mergeChildren', title: '数组合并' },
            //                                             { value: 'image', title: '缩略图' },
            //                                             { value: 'progress', title: '进度条' },
            //                                             { value: 'link', title: '链接' },
            //                                             { value: 'dialog', title: '弹窗' },
            //                                             { value: 'tag', title: '标签' },
            //                                             { value: 'ranking', title: '排名' },
            //                                             { value: 'lineChart', title: '折线图' },
            //                                         ],
            //                                     },
            //                                 },
            //                             },

            //                             {
            //                                 name: 'arrow',
            //                                 title: '显示升降',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                             },
            //                             {
            //                                 name: 'contrastState',
            //                                 title: '是否对比',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                                 condition: (target) => {
            //                                     const numberColorState = target.parent.getPropValue('numberColorState');
            //                                     return !numberColorState && true;
            //                                 },
            //                             },
            //                             {
            //                                 name: 'data',
            //                                 type: 'group',
            //                                 display: 'accordion',
            //                                 title: '对比配置',
            //                                 items: [
            //                                     {
            //                                         name: 'contrastKey',
            //                                         title: '对比字段',
            //                                         display: 'inline',
            //                                         extraProps: {
            //                                             setValue: (target, value) => {
            //                                                 const columns = target.getProps().getPropValue('columnsCopy');
            //                                                 const selectColumn = _.find(columns, { dataIndex: `${value}` })
            //                                                 target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
            //                                             }
            //                                         },
            //                                         setter: {
            //                                             componentName: 'SelectSetter',
            //                                             props: (target) => {
            //                                                 const columns = target.getProps().getPropValue('columnsCopy');

            //                                                 let option = [];
            //                                                 if (columns && columns.length) {
            //                                                     columns.map(item => {
            //                                                         option.push(...filterColumns(item));
            //                                                     })
            //                                                 }

            //                                                 return {
            //                                                     options: option,
            //                                                     showSearch: true,
            //                                                 }
            //                                             }
            //                                         },
            //                                     },
            //                                     {
            //                                         name: 'largerValue',
            //                                         title: '较大值颜色',
            //                                         display: 'inline',
            //                                         defaultValue: '#F65B5B',
            //                                         setter: 'ColorSetter',
            //                                     },
            //                                     {
            //                                         name: 'smallerValue',
            //                                         title: '较小值颜色',
            //                                         display: 'inline',
            //                                         defaultValue: '#59C252',
            //                                         setter: 'ColorSetter',
            //                                     },
            //                                 ],
            //                                 condition: (target) => {
            //                                     const formatType = target.parent.getPropValue('type');
            //                                     const contrastState = target.parent.getPropValue('contrastState');
            //                                     return ['text', 'number'].includes(formatType) && contrastState === true;
            //                                 },
            //                             },
            //                             {
            //                                 name: 'numberColorState',
            //                                 title: '数字状态颜色',
            //                                 display: 'inline',
            //                                 initialValue: false,
            //                                 setter: 'BoolSetter',
            //                                 extraProps: {
            //                                     setValue: (target, value) => {
            //                                         target.parent.setPropValue('numberColorState', value);
            //                                         if (value) {
            //                                             const colorRules = [
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 80,
            //                                                     color: '#FB8437',
            //                                                 },
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 95,
            //                                                     color: '#489CFF',
            //                                                 },
            //                                                 {
            //                                                     rule: '>=',
            //                                                     value: 100,
            //                                                     color: '#59C252',
            //                                                 },
            //                                                 {
            //                                                     rule: '<',
            //                                                     value: 80,
            //                                                     color: '#F65B5B',
            //                                                 },
            //                                             ]
            //                                             target.parent.setPropValue('colorRules', colorRules);
            //                                         }
            //                                     },
            //                                 },
            //                                 condition: (target) => {
            //                                     const formatType = target.parent.getPropValue('type');
            //                                     const contrastState = target.parent.getPropValue('contrastState');
            //                                     return formatType !== 'merge' && formatType !== 'mergeChildren' && !contrastState;
            //                                 },
            //                             },
            //                             colorRules,
            //                             {
            //                                 name: 'emptyValueShow',
            //                                 title: '保留空值显示',
            //                                 display: 'inline',
            //                                 defaultValue: false,
            //                                 setter: 'BoolSetter',
            //                             },
            //                             {
            //                                 name: 'defaultValue',
            //                                 title: '默认空值',
            //                                 display: 'inline',
            //                                 defaultValue: '-',
            //                                 setter: 'StringSetter',
            //                                 condition: (target) => {
            //                                     return !target.parent.getPropValue('emptyValueShow');
            //                                 },
            //                             },
            //                             {
            //                                 name: 'data',
            //                                 title: '交互设置',
            //                                 type: 'group',
            //                                 display: 'accordion',
            //                                 condition: (target) => {
            //                                     const formatType = target.parent.getPropValue('type');
            //                                     return formatType && ['dialog', 'link'].includes(formatType);
            //                                 },
            //                                 items: [
            //                                     {
            //                                         name: 'showText',
            //                                         title: '显示名称',
            //                                         setter: 'StringSetter',
            //                                         defaultValue: '默认名称',
            //                                     },
            //                                     {
            //                                         name: 'linkType',
            //                                         title: '跳转方式',
            //                                         setter: {
            //                                             componentName: 'RadioGroupSetter',
            //                                             props: {
            //                                                 options: [
            //                                                     { label: '抽屉式弹窗', value: '_drawer' },
            //                                                     { label: '当前窗口', value: '_self' },
            //                                                     { label: '新窗口', value: '_blank' },
            //                                                 ],
            //                                             },
            //                                         },
            //                                     },
            //                                 ]
            //                             },
            //                         ]
            //                     }
            //                 },
            //                 initialValue: () => {
            //                     return {
            //                         title: '列标题',
            //                         type: 'text',
            //                         dataIndex: 'dataIndex',
            //                         assistants: [],
            //                         children: [],
            //                     };
            //                 },
            //             }
            //         },
            //     },
            // },
            ...flexibleRouteWithoutLinkProps,
          ],
        },
      },
      initialValue: () => {
        return {
          title: '列标题',
          type: 'text',
          dataIndex: Date.now() + Math.floor(Math.random() * 1000),
          assistants: [],
          children: [],
        };
      },
    },
  },
};

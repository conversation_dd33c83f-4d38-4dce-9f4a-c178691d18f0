export const operateColumns = {
    name: 'actionColumnProps',
    title: '操作列',
    extraProps: {
        display: 'accordion',
        defaultCollapsed: true,
    },
    setter: {
        componentName: 'ObjectSetter',
        props: {
            display: 'drawer',
            config: {
                items: [
                    {
                        name: 'actionColumnShow',
                        title: '是否显示',
                        initialValue: false,
                        setter: 'BoolSetter',
                    },
                    {
                        name: 'width',
                        title: '宽度',
                        display: 'inline',
                        setter: 'NumberSetter',
                    },
                    {
                        name: 'actionColumnTitle',
                        title: '标题',
                        extraProps: {
                            display: 'inline',
                            defaultValue: '操作',
                        },
                        setter: {
                            componentName: 'StringSetter',
                        },
                    },
                    {
                        name: 'fixed',
                        title: '锁列',
                        display: 'inline',
                        initialValue: '',
                        setter: {
                            componentName: 'RadioGroupSetter',
                            props: {
                                options: [
                                    {
                                        title: '左侧',
                                        value: 'left',
                                        tip: 'left',
                                    },
                                    {
                                        title: '不锁',
                                        value: 'none',
                                        tip: 'none',
                                    },
                                    {
                                        title: '右侧',
                                        value: 'right',
                                        tip: 'right',
                                    },
                                ],
                                compact: false,
                            },
                        },
                    },],
            }
        }
    }
}

const paramItem = {
    componentName: 'ArraySetter',
    props: {
        itemSetter: {
            componentName: 'ObjectSetter',
            props: {
                config: {
                    items: [
                        {
                            name: 'name',
                            title: '参数名',
                            display: 'inline',
                            isRequired: true,
                            setter: 'StringSetter',
                        },
                        {
                            name: 'value',
                            title: '参数值',
                            isRequired: true,
                            display: 'inline',
                            setter: {
                                componentName: 'SelectSetter',
                                props: (target) => {
                                    const dim = target.getProps().getPropValue('dim');
                                    let options = [];
                                    if (dim && dim.length) {
                                        dim.map(item => {
                                            if (item?.fieldMeta?.metaType === 'DIM') {
                                                options.push({
                                                    label: item?.title,
                                                    value: item?.ref,
                                                });
                                            }
                                        })
                                    }
                                    return {
                                        options,
                                        showSearch: true,
                                    }
                                }
                            },
                        },
                    ]
                }
            },
            initialValue: () => ({
                name: '默认名称',
                dataIndex: 'dataIndex',
            }),
        }
    }
}

export const operateButtons = {
    name: 'buttonGroup',
    title: '按钮列按钮',
    extraProps: {
        display: 'accordion',
        defaultCollapsed: true,
    },
    setter: {
        componentName: 'ArraySetter',
        props: {
            itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'buttonItemTitle',
                                title: '名称',
                                initialValue: '操作',
                                isRequired: true,
                                setter: 'StringSetter',
                            },
                            {
                                name: 'buttonItemType',
                                title: '按钮样式',
                                display: 'inline',
                                isRequired: true,
                                initialValue: 'primary',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                title: '普通按钮',
                                                value: 'normal',
                                            },
                                            {
                                                title: '主按钮',
                                                value: 'primary',
                                            },
                                            {
                                                title: '链接',
                                                value: 'link',
                                            },
                                            {
                                                title: '文本',
                                                value: 'text',
                                            },
                                            {
                                                title: '禁用',
                                                value: 'dashed',
                                            },
                                        ],
                                    },
                                },
                            },
                            // {
                            //     name: 'actionType',
                            //     title: '操作类型',
                            //     // condition: hideProp,
                            //     display: 'inline',
                            //     initialValue: 'link',
                            //     setter: {
                            //         componentName: 'RadioGroupSetter',
                            //         props: {
                            //             options: [
                            //                 {
                            //                     title: '弹窗',
                            //                     value: 'formDialog',
                            //                 },
                            //                 {
                            //                     title: '链接',
                            //                     value: 'link',
                            //                 },
                            //             ],
                            //         },
                            //     },
                            // },
                            // {
                            //     name: 'linkType',
                            //     title: '跳转方式',
                            //     condition: (target) => {
                            //         return target.parent.getPropValue('actionType') === 'link';
                            //     },
                            //     display: 'inline',
                            //     initialValue: 'link',
                            //     setter: {
                            //         componentName: 'RadioGroupSetter',
                            //         props: {
                            //             options: [
                            //                 {
                            //                     title: '当前窗口',
                            //                     value: '_self',
                            //                 },
                            //                 {
                            //                     title: '新窗口',
                            //                     value: '_block',
                            //                 },
                            //             ],
                            //         },
                            //     },
                            // },
                            // {
                            //     name: 'jumpType',
                            //     title: '跳转类型',
                            //     condition: (target) => {
                            //         return target.parent.getPropValue('actionType') === 'link';
                            //     },
                            //     display: 'inline',
                            //     setter: {
                            //         componentName: 'RadioGroupSetter',
                            //         props: {
                            //             options: [
                            //                 {
                            //                     title: '内部页面',
                            //                     value: 'internalPage',
                            //                 },
                            //                 {
                            //                     title: '外部链接',
                            //                     value: 'externalLinks',
                            //                 },
                            //             ],
                            //         },
                            //     },
                            // },
                            // {
                            //     name: 'jumpPage',
                            //     title: '跳转页面',
                            //     display: 'inline',
                            //     setter: {
                            //         componentName: 'SelectSetter',
                            //         props: {
                            //             options: [

                            //             ],
                            //         },
                            //     },
                            //     condition: (target) => {
                            //         return target.parent.getPropValue('jumpType') === 'internalPage';
                            //     },
                            // },
                            // {
                            //     name: 'jumpLink',
                            //     title: '跳转链接',
                            //     display: 'inline',
                            //     setter: 'StringSetter',
                            //     defaultValue: 'https://test-bi.bananain.cn/polaris/r940e0cccc1f7439dbd57bf6/BeHmZsiUbK/AcYYcBLpsI/p472b5afc8af24c84ba8b946?tfdf2b26d5bf04578908182c=',
                            //     condition: (target) => {
                            //         return target.parent.getPropValue('jumpType') === 'externalLinks';
                            //     },
                            // },
                            // {
                            //     name: 'parameter',
                            //     title: '携带参数',
                            //     display: 'block',
                            //     condition: (target) => {
                            //         return target.parent.getPropValue('actionType') === 'link';
                            //     },
                            //     setter: paramItem,
                            // },
                            {
                              name: 'linkConfig',
                              title: '跳转配置',
                              setter: 'LinkBoxSetter',
                            },
                            {
                                name: 'onClick',
                                title: '点击事件',
                                setter: 'FunctionSetter',
                            },
                        ]
                    },
                },
                initialValue: () => ({
                    buttonItemTitle: '默认名称',
                    parameter: [],
                    dataIndex: 'dataIndex',
                }),
            },
        },
    },
}

import { uuid, mockProTableRow, mockId, filterColumns, setFixed, getAllColumns } from './utils';
import { IProps } from '../../types/index';
import debounce from 'lodash/debounce';
import columnsChildrenProp from './columns-children-prop';
import columnsAssistantsProp from './columns-assistants-prop';
import colorRules from './colorRules-prop';
import textColorRules from './textRules-prop';
import { isJSExpression } from '../../utils';
import _ from 'lodash';
import dateColorRules from './date-color-rules';
import popupProp from './popup-prop';
import { flexibleRouteProps } from './flexible-route-props';

export const columnsField: IProps = {
  type: 'field',
  name: 'columns',
  title: '数据列',
  extraProps: {
    display: 'accordion',
  },
  condition: (target) => {
    return target.parent.getPropValue('showHeadConfig') || false;
  },
  setValue: debounce((field, columns) => {
    const _columns = isJSExpression(columns) ? columns.mock : columns;
    if (!_columns || !Array.isArray(_columns) || !_columns.length) {
      return;
    }
    const { node } = field;
    const dataSource = node.getPropValue('dataSource') || [];
    const _dataSource = isJSExpression(dataSource) ? dataSource.mock : dataSource;
    if (!_dataSource || !Array.isArray(_dataSource) || !_dataSource.length) {
      return;
    }
    const primaryKey = node.getPropValue('primaryKey') || 'id';
    const mockRow = mockProTableRow(columns);
    const newData = dataSource.map((item) => ({
      [primaryKey]: mockId(),
      ...mockRow,
      ...item,
    }));
    // if (!deepEqual(newData, dataSource)) {
    //   node.setPropValue('dataSource', newData);
    // }
  }),
  setter: {
    componentName: 'ArraySetter',
    props: {
      itemSetter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'title',
                title: '标题',
                isRequired: true,
                setter: 'StringSetter',
              },
              {
                name: 'warpWidth',
                title: '内容宽度',
                display: 'inline',
                initialValue: 100,
                setter: 'NumberSetter',
              },
              {
                name: 'width',
                title: '表格宽度',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'isShowHeader',
                title: '显示表头',
                display: 'inline',
                setter: 'BoolSetter',
              },
              {
                name: 'type',
                title: '数据类型',
                display: 'inline',
                initialValue: 'text',
                isRequired: true,
                extraProps: {
                  setValue: (target, value) => {
                    target.parent.setPropValue('sorter', false);

                    if (value !== 'text') {
                      target.parent.setPropValue('hasPopup', false);
                    }

                    switch (value) {
                      case 'number':
                        target.parent.setPropValue('align', 'right');
                        target.parent.setPropValue('textColorState', false);
                        break;
                      case 'text':
                        target.parent.setPropValue('numberColorState', false);
                        break;
                      case 'image':
                        target.parent.setPropValue('warpWidth', 38);
                        break;
                      case 'progress':
                        target.parent.setPropValue('warpWidth', 180);
                        break;
                      default:
                        break;
                    }
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'text', title: '文本' },
                      { value: 'number', title: '数字' },
                      { value: 'progress', title: '进度条' },
                      { value: 'link', title: '链接' },
                      { value: 'hyperlink', title: '超链接' },
                      { value: 'video', title: '视频链接' },
                      { value: 'dialog', title: '弹窗' },
                      { value: 'tag', title: '标签' },
                      { value: 'ranking', title: '红榜排名' },
                      { value: 'blackRanking', title: '黑榜排名' },
                      { value: 'merge', title: '主副合并' },
                      { value: 'mergeChhildren', title: '数组合并' },
                      { value: 'image', title: '缩略图' },
                      { value: 'lineChart', title: '折线图' },
                      { value: 'date', title: '日期' },
                    ],
                  },
                },
              },
              {
                name: 'subTitle', // 只有主副指标有这个数据
                title: '副标题',
                display: 'inline',
                defaultValue: '',
                setter: 'StringSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'merge';
                },
              },
              {
                name: 'dataIndex',
                title: '数据字段',
                display: 'inline',
                initialValue: (currentValue, defaultValue) =>
                  currentValue || defaultValue || `data-${uuid()}`,
                extraProps: {
                  setValue: (target, value) => {
                    const columns = target.getProps().getPropValue('columnsCopy');
                    const allColumns = getAllColumns(columns);
                    const selectColumn = _.find(allColumns, { dataIndex: `${value}` });
                    target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: (target) => {
                    const columns = target.getProps().getPropValue('columnsCopy');

                    const option = [];
                    if (columns && columns.length) {
                      columns.forEach((item) => {
                        option.push(...filterColumns(item));
                      });
                    }

                    return {
                      options: option,
                      showSearch: true,
                    };
                  },
                },
              },
              {
                name: 'align',
                title: '对齐方式',
                display: 'inline',
                initialValue: 'left',
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      {
                        value: 'left',
                        title: '居左',
                      },
                      {
                        value: 'center',
                        title: '居中',
                      },
                      {
                        value: 'right',
                        title: '居右',
                      },
                    ],
                  },
                },
              },
              {
                name: 'fixed',
                title: '锁列',
                display: 'inline',
                initialValue: 'none',
                extraProps: {
                  setValue: (target, value) => {
                    const children = target.parent.getPropValue('children');
                    const childrens = setFixed(children, value);
                    target.parent.setPropValue('children', childrens);
                  },
                },
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      {
                        title: '左侧',
                        value: 'left',
                        tip: 'left',
                      },
                      {
                        title: '不锁',
                        value: 'none',
                        tip: 'none',
                      },
                      {
                        title: '右侧',
                        value: 'right',
                        tip: 'right',
                      },
                    ],
                    compact: false,
                  },
                },
              },
              {
                name: 'sorter',
                title: '排序',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const type = target.parent.getPropValue('type');
                  return type !== 'mergeChhildren';
                },
              },
              {
                name: 'emptyValueShow',
                title: '保留空值显示',
                display: 'inline',
                defaultValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'showEmptyValue',
                title: '图片隐藏空值',
                display: 'inline',
                defaultValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'image';
                },
              },
              {
                name: 'defaultValue',
                title: '默认空值',
                display: 'inline',
                defaultValue: '-',
                setter: 'StringSetter',
                condition: (target) => {
                  return !target.parent.getPropValue('emptyValueShow');
                },
              },
              // 高级设置
              {
                name: 'explanationIconShow',
                title: '展示图标',
                setter: 'BoolSetter',
              },
              // {
              //   name: 'tooltipTitle',
              //   title: '释义标题',
              //   display: 'inline',
              //   initialValue: '',
              //   setter: 'StringSetter',
              // },
              {
                name: 'explanation',
                title: '释义内容',
                display: 'inline',
                initialValue: '',
                extraProps: {
                  setValue: (target, val) => {
                    const { jnIndicatorList } = window;
                    jnIndicatorList &&
                      jnIndicatorList?.forEach((item) => {
                        if (item.value === val) {
                          target.parent.setPropValue('title', item.label);
                        }
                      });
                  },
                },
                setter: 'JNDicatorSetter',
              },
              {
                name: 'dirllMeta',
                title: '下钻说明',
                display: 'inline',
                initialValue: '',
                setter: 'TextAreaSetter',
              },
              {
                name: 'arrow',
                title: '显示升降',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType !== 'mergeChhildren';
                },
              },
              {
                name: 'contrastState',
                title: '是否对比',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const type = target.parent.getPropValue('type');
                  const textColorState = target.parent.getPropValue('textColorState');
                  const numberColorState = target.parent.getPropValue('numberColorState');
                  return (
                    !textColorState &&
                    !numberColorState &&
                    ['text', 'number', 'merge'].includes(type)
                  );
                },
              },
              {
                name: 'data',
                type: 'group',
                display: 'accordion',
                title: '对比配置',
                items: [
                  {
                    name: 'contrastKey',
                    title: '对比字段',
                    display: 'inline',
                    extraProps: {
                      setValue: (target, value) => {
                        const columns = target.getProps().getPropValue('columnsCopy');
                        const selectColumn = _.find(columns, { dataIndex: `${value}` });
                        target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                      },
                    },
                    setter: {
                      componentName: 'SelectSetter',
                      props: (target) => {
                        const columns = target.getProps().getPropValue('columnsCopy');

                        const option = [];
                        if (columns && columns.length) {
                          columns.forEach((item) => {
                            option.push(...filterColumns(item));
                          });
                        }

                        return {
                          options: option,
                          showSearch: true,
                        };
                      },
                    },
                  },
                  {
                    name: 'largerValue',
                    title: '较大值颜色',
                    display: 'inline',
                    defaultValue: '#F65B5B',
                    setter: 'ColorSetter',
                  },
                  {
                    name: 'smallerValue',
                    title: '较小值颜色',
                    display: 'inline',
                    defaultValue: '#59C252',
                    setter: 'ColorSetter',
                  },
                ],
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['text', 'number', 'merge'].includes(formatType) && contrastState === true;
                },
              },
              {
                name: 'numberColorState',
                title: '数字状态颜色',
                display: 'inline',
                extraProps: {
                  setValue: (target, value) => {
                    target.parent.setPropValue('numberColorState', value);
                    if (value) {
                      const colorRules = [
                        {
                          rule: '<',
                          value: 90,
                          color: '#F65B5B',
                        },
                        {
                          rule: '>=',
                          value: 90,
                          color: '#FB8437',
                        },
                        {
                          rule: '>=',
                          value: 100,
                          color: '#59C252',
                        },
                      ];
                      target.parent.setPropValue('colorRules', colorRules);
                    }
                  },
                },
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['number', 'merge', 'progress'].includes(formatType) && !contrastState;
                },
              },
              colorRules,
              {
                name: 'textColorState',
                title: '文本状态颜色',
                display: 'inline',
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['text'].includes(formatType) && !contrastState;
                },
              },
              textColorRules,
              dateColorRules,
              ...flexibleRouteProps,
              {
                name: 'assistants',
                title: '合并内容',
                display: 'block',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'merge' || false;
                },
                setter: columnsAssistantsProp,
              },
              {
                name: 'children',
                title: '合并内容',
                display: 'block',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'mergeChhildren' || false;
                },
                setter: columnsChildrenProp,
              },

              {
                name: 'data',
                title: '交互设置',
                type: 'group',
                display: 'accordion',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType && ['dialog', 'link', 'video'].includes(formatType);
                },
                items: [
                  {
                    name: 'showText',
                    title: '显示名称',
                    setter: 'StringSetter',
                    defaultValue: '默认名称',
                  },
                  {
                    name: 'showDefaultLink',
                    title: '显示链接',
                    setter: 'BoolSetter',
                    defaultValue: false,
                  },
                  {
                    name: 'linkType',
                    title: '跳转方式',
                    setter: {
                      componentName: 'RadioGroupSetter',
                      props: {
                        options: [
                          { label: '抽屉式弹窗', value: '_drawer' },
                          { label: '新窗口', value: '_blank' },
                        ],
                      },
                    },
                    condition: (target) => {
                      const formatType = target.parent.getPropValue('type');
                      return formatType && !['video'].includes(formatType);
                    },
                  },
                ],
              },
              {
                name: 'hyperlinkConfig',
                title: '交互设置',
                type: 'group',
                display: 'accordion',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'hyperlink';
                },
                items: [
                  {
                    name: 'bindHyperlinkField',
                    title: '关联跳转链接',
                    setter: 'BoolSetter',
                    defaultValue: false,
                  },
                  {
                    name: 'hyperlinkField',
                    title: '选择字段',
                    condition: (target) => {
                      const baseCondition = target.parent.getPropValue('bindHyperlinkField');
                      return baseCondition;
                    },
                    setter: {
                      componentName: 'SelectSetter',
                      props: (target) => {
                        const columns = target.getProps().getPropValue('columnsCopy');

                        const options = [];
                        if (columns && columns.length) {
                          columns.forEach((item) => {
                            options.push(...filterColumns(item));
                          });
                        }

                        return {
                          options: options,
                          showSearch: true,
                        };
                      },
                    },
                  },
                  {
                    name: 'linkType',
                    title: '跳转方式',
                    setter: {
                      componentName: 'RadioGroupSetter',
                      props: {
                        options: [
                          { label: '抽屉式弹窗', value: '_drawer' },
                          { label: '新窗口', value: '_blank' },
                        ],
                      },
                    },
                    condition: (target) => {
                      const formatType = target.parent.getPropValue('type');
                      return formatType === 'hyperlink';
                    },
                  },
                ],
              },
              {
                name: 'hasPopup',
                title: '浮窗',
                display: 'inline',
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType && ['text'].includes(formatType);
                },
              },
              popupProp,
            ],
          },
        },
        initialValue: () => {
          return {
            title: '列标题',
            type: 'text',
            dataIndex: Date.now() + Math.floor(Math.random() * 1000),
            assistants: [],
            children: [],
            isShowHeader: true,
          };
        },
      },
    },
  },
};

// 暂时性移除
export const columnsFieldWithoutHyperLink: IProps = {
  type: 'field',
  name: 'columns',
  title: '数据列',
  extraProps: {
    display: 'accordion',
  },
  condition: (target) => {
    return target.parent.getPropValue('showHeadConfig') || false;
  },
  setValue: debounce((field, columns) => {
    const _columns = isJSExpression(columns) ? columns.mock : columns;
    if (!_columns || !Array.isArray(_columns) || !_columns.length) {
      return;
    }
    const { node } = field;
    const dataSource = node.getPropValue('dataSource') || [];
    const _dataSource = isJSExpression(dataSource) ? dataSource.mock : dataSource;
    if (!_dataSource || !Array.isArray(_dataSource) || !_dataSource.length) {
      return;
    }
    const primaryKey = node.getPropValue('primaryKey') || 'id';
    const mockRow = mockProTableRow(columns);
    const newData = dataSource.map((item) => ({
      [primaryKey]: mockId(),
      ...mockRow,
      ...item,
    }));
    // if (!deepEqual(newData, dataSource)) {
    //   node.setPropValue('dataSource', newData);
    // }
  }),
  setter: {
    componentName: 'ArraySetter',
    props: {
      itemSetter: {
        componentName: 'ObjectSetter',
        props: {
          config: {
            items: [
              {
                name: 'title',
                title: '标题',
                isRequired: true,
                setter: 'StringSetter',
              },
              {
                name: 'warpWidth',
                title: '内容宽度',
                display: 'inline',
                initialValue: 100,
                setter: 'NumberSetter',
              },
              {
                name: 'width',
                title: '表格宽度',
                display: 'inline',
                setter: 'NumberSetter',
              },
              {
                name: 'isShowHeader',
                title: '显示表头',
                display: 'inline',
                setter: 'BoolSetter',
              },
              {
                name: 'type',
                title: '数据类型',
                display: 'inline',
                initialValue: 'text',
                isRequired: true,
                extraProps: {
                  setValue: (target, value) => {
                    target.parent.setPropValue('sorter', false);

                    switch (value) {
                      case 'number':
                        target.parent.setPropValue('align', 'right');
                        target.parent.setPropValue('textColorState', false);
                        break;
                      case 'text':
                        target.parent.setPropValue('numberColorState', false);
                        break;
                      case 'image':
                        target.parent.setPropValue('warpWidth', 38);
                        break;
                      case 'progress':
                        target.parent.setPropValue('warpWidth', 180);
                        break;
                      default:
                        break;
                    }
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: {
                    options: [
                      { value: 'text', title: '文本' },
                      { value: 'number', title: '数字' },
                      { value: 'progress', title: '进度条' },
                      { value: 'link', title: '链接' },
                      { value: 'video', title: '视频链接' },
                      { value: 'dialog', title: '弹窗' },
                      { value: 'tag', title: '标签' },
                      { value: 'ranking', title: '红榜排名' },
                      { value: 'blackRanking', title: '黑榜排名' },
                      { value: 'merge', title: '主副合并' },
                      { value: 'mergeChhildren', title: '数组合并' },
                      { value: 'image', title: '缩略图' },
                      { value: 'lineChart', title: '折线图' },
                      { value: 'date', title: '日期' },
                    ],
                  },
                },
              },
              {
                name: 'subTitle', // 只有主副指标有这个数据
                title: '副标题',
                display: 'inline',
                defaultValue: '',
                setter: 'StringSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'merge';
                },
              },
              {
                name: 'dataIndex',
                title: '数据字段',
                display: 'inline',
                initialValue: (currentValue, defaultValue) =>
                  currentValue || defaultValue || `data-${uuid()}`,
                extraProps: {
                  setValue: (target, value) => {
                    const columns = target.getProps().getPropValue('columnsCopy');
                    const allColumns = getAllColumns(columns);
                    const selectColumn = _.find(allColumns, { dataIndex: `${value}` });
                    target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                  },
                },
                setter: {
                  componentName: 'SelectSetter',
                  props: (target) => {
                    const columns = target.getProps().getPropValue('columnsCopy');

                    const option = [];
                    if (columns && columns.length) {
                      columns.forEach((item) => {
                        option.push(...filterColumns(item));
                      });
                    }

                    return {
                      options: option,
                      showSearch: true,
                    };
                  },
                },
              },
              {
                name: 'align',
                title: '对齐方式',
                display: 'inline',
                initialValue: 'left',
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      {
                        value: 'left',
                        title: '居左',
                      },
                      {
                        value: 'center',
                        title: '居中',
                      },
                      {
                        value: 'right',
                        title: '居右',
                      },
                    ],
                  },
                },
              },
              {
                name: 'fixed',
                title: '锁列',
                display: 'inline',
                initialValue: 'none',
                extraProps: {
                  setValue: (target, value) => {
                    const children = target.parent.getPropValue('children');
                    const childrens = setFixed(children, value);
                    target.parent.setPropValue('children', childrens);
                  },
                },
                setter: {
                  componentName: 'RadioGroupSetter',
                  props: {
                    options: [
                      {
                        title: '左侧',
                        value: 'left',
                        tip: 'left',
                      },
                      {
                        title: '不锁',
                        value: 'none',
                        tip: 'none',
                      },
                      {
                        title: '右侧',
                        value: 'right',
                        tip: 'right',
                      },
                    ],
                    compact: false,
                  },
                },
              },
              {
                name: 'sorter',
                title: '排序',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const type = target.parent.getPropValue('type');
                  return type !== 'mergeChhildren';
                },
              },
              {
                name: 'emptyValueShow',
                title: '保留空值显示',
                display: 'inline',
                defaultValue: false,
                setter: 'BoolSetter',
              },
              {
                name: 'showEmptyValue',
                title: '图片隐藏空值',
                display: 'inline',
                defaultValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'image';
                },
              },
              {
                name: 'defaultValue',
                title: '默认空值',
                display: 'inline',
                defaultValue: '-',
                setter: 'StringSetter',
                condition: (target) => {
                  return !target.parent.getPropValue('emptyValueShow');
                },
              },
              // 高级设置
              {
                name: 'explanationIconShow',
                title: '展示图标',
                setter: 'BoolSetter',
              },
              // {
              //   name: 'tooltipTitle',
              //   title: '释义标题',
              //   display: 'inline',
              //   initialValue: '',
              //   setter: 'StringSetter',
              // },
              {
                name: 'explanation',
                title: '释义内容',
                display: 'inline',
                initialValue: '',
                extraProps: {
                  setValue: (target, val) => {
                    const { jnIndicatorList } = window;
                    jnIndicatorList &&
                      jnIndicatorList?.forEach((item) => {
                        if (item.value === val) {
                          target.parent.setPropValue('title', item.label);
                        }
                      });
                  },
                },
                setter: 'JNDicatorSetter',
              },
              {
                name: 'dirllMeta',
                title: '下钻说明',
                display: 'inline',
                initialValue: '',
                setter: 'TextAreaSetter',
              },
              {
                name: 'arrow',
                title: '显示升降',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType !== 'mergeChhildren';
                },
              },
              {
                name: 'contrastState',
                title: '是否对比',
                display: 'inline',
                initialValue: false,
                setter: 'BoolSetter',
                condition: (target) => {
                  const type = target.parent.getPropValue('type');
                  const textColorState = target.parent.getPropValue('textColorState');
                  const numberColorState = target.parent.getPropValue('numberColorState');
                  return (
                    !textColorState &&
                    !numberColorState &&
                    ['text', 'number', 'merge'].includes(type)
                  );
                },
              },
              {
                name: 'data',
                type: 'group',
                display: 'accordion',
                title: '对比配置',
                items: [
                  {
                    name: 'contrastKey',
                    title: '对比字段',
                    display: 'inline',
                    extraProps: {
                      setValue: (target, value) => {
                        const columns = target.getProps().getPropValue('columnsCopy');
                        const selectColumn = _.find(columns, { dataIndex: `${value}` });
                        target.parent.setPropValue('fieldMeta', selectColumn?.fieldMeta);
                      },
                    },
                    setter: {
                      componentName: 'SelectSetter',
                      props: (target) => {
                        const columns = target.getProps().getPropValue('columnsCopy');

                        const option = [];
                        if (columns && columns.length) {
                          columns.forEach((item) => {
                            option.push(...filterColumns(item));
                          });
                        }

                        return {
                          options: option,
                          showSearch: true,
                        };
                      },
                    },
                  },
                  {
                    name: 'largerValue',
                    title: '较大值颜色',
                    display: 'inline',
                    defaultValue: '#F65B5B',
                    setter: 'ColorSetter',
                  },
                  {
                    name: 'smallerValue',
                    title: '较小值颜色',
                    display: 'inline',
                    defaultValue: '#59C252',
                    setter: 'ColorSetter',
                  },
                ],
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['text', 'number', 'merge'].includes(formatType) && contrastState === true;
                },
              },
              {
                name: 'numberColorState',
                title: '数字状态颜色',
                display: 'inline',
                extraProps: {
                  setValue: (target, value) => {
                    target.parent.setPropValue('numberColorState', value);
                    if (value) {
                      const colorRules = [
                        {
                          rule: '<',
                          value: 90,
                          color: '#F65B5B',
                        },
                        {
                          rule: '>=',
                          value: 90,
                          color: '#FB8437',
                        },
                        {
                          rule: '>=',
                          value: 100,
                          color: '#59C252',
                        },
                      ];
                      target.parent.setPropValue('colorRules', colorRules);
                    }
                  },
                },
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['number', 'merge', 'progress'].includes(formatType) && !contrastState;
                },
              },
              colorRules,
              {
                name: 'textColorState',
                title: '文本状态颜色',
                display: 'inline',
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  const contrastState = target.parent.getPropValue('contrastState');
                  return ['text'].includes(formatType) && !contrastState;
                },
              },
              textColorRules,
              dateColorRules,
              ...flexibleRouteProps,
              {
                name: 'assistants',
                title: '合并内容',
                display: 'block',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'merge' || false;
                },
                setter: columnsAssistantsProp,
              },
              {
                name: 'children',
                title: '合并内容',
                display: 'block',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'mergeChhildren' || false;
                },
                setter: columnsChildrenProp,
              },

              {
                name: 'data',
                title: '交互设置',
                type: 'group',
                display: 'accordion',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType && ['dialog', 'link', 'video'].includes(formatType);
                },
                items: [
                  {
                    name: 'showText',
                    title: '显示名称',
                    setter: 'StringSetter',
                    defaultValue: '默认名称',
                  },
                  {
                    name: 'showDefaultLink',
                    title: '显示链接',
                    setter: 'BoolSetter',
                    defaultValue: false,
                  },
                  {
                    name: 'linkType',
                    title: '跳转方式',
                    setter: {
                      componentName: 'RadioGroupSetter',
                      props: {
                        options: [
                          { label: '抽屉式弹窗', value: '_drawer' },
                          { label: '新窗口', value: '_blank' },
                        ],
                      },
                    },
                    condition: (target) => {
                      const formatType = target.parent.getPropValue('type');
                      return formatType && !['video'].includes(formatType);
                    },
                  },
                ],
              },
              {
                name: 'hyperlinkConfig',
                title: '交互设置',
                type: 'group',
                display: 'accordion',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType === 'hyperlink';
                },
                items: [
                  {
                    name: 'bindHyperlinkField',
                    title: '关联跳转链接',
                    setter: 'BoolSetter',
                    defaultValue: false,
                  },
                  {
                    name: 'hyperlinkField',
                    title: '选择字段',
                    condition: (target) => {
                      const baseCondition = target.parent.getPropValue('bindHyperlinkField');
                      return baseCondition;
                    },
                    setter: {
                      componentName: 'SelectSetter',
                      props: (target) => {
                        const columns = target.getProps().getPropValue('columnsCopy');

                        const options = [];
                        if (columns && columns.length) {
                          columns.forEach((item) => {
                            options.push(...filterColumns(item));
                          });
                        }

                        return {
                          options: options,
                          showSearch: true,
                        };
                      },
                    },
                  },
                  {
                    name: 'linkType',
                    title: '跳转方式',
                    setter: {
                      componentName: 'RadioGroupSetter',
                      props: {
                        options: [
                          { label: '抽屉式弹窗', value: '_drawer' },
                          { label: '新窗口', value: '_blank' },
                        ],
                      },
                    },
                    condition: (target) => {
                      const formatType = target.parent.getPropValue('type');
                      return formatType === 'hyperlink';
                    },
                  },
                ],
              },
              {
                name: 'hasPopup',
                title: '浮窗',
                display: 'inline',
                setter: 'BoolSetter',
                condition: (target) => {
                  const formatType = target.parent.getPropValue('type');
                  return formatType && ['text'].includes(formatType);
                },
              },
              popupProp,
            ],
          },
        },
        initialValue: () => {
          return {
            title: '列标题',
            type: 'text',
            dataIndex: Date.now() + Math.floor(Math.random() * 1000),
            assistants: [],
            children: [],
            isShowHeader: true,
          };
        },
      },
    },
  },
};

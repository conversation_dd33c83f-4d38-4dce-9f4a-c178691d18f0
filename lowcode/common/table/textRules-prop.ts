import { colors } from "./color.config";

export default {
    type: 'field',
    name: 'textColorRules',
    title: '自定义规则',
    display: 'accordion',
    setter: {
        componentName: 'ArraySetter',
        props: {
            itemSetter: {
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'rule',
                                title: '规则',
                                display: 'inline',
                                isRequired: true,
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                title: '等于',
                                                value: '等于'
                                            },
                                            {
                                                title: '包含',
                                                value: '包含'
                                            },
                                        ]
                                    }
                                },
                            },
                            {
                                name: 'value',
                                title: '文本',
                                display: 'inline',
                                isRequired: true,
                                setter: 'StringSetter',
                            },
                            {
                                name: 'color',
                                title: '颜色',
                                display: 'inline',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: colors,
                                    },
                                },
                            },
                            {
                                name: 'rowState',
                                title: '行渲染',
                                display: 'inline',
                                setter: 'BoolSetter',
                            },
                        ],
                    },
                },
                initialValue: () => {
                    return {
                        rule: '',
                        rowState: false,
                    };
                },
            },
        },
    },
    condition: (target) => {
        const formatType = target.parent.getPropValue('type');
        const textColorState = target.parent.getPropValue('textColorState');
        return ['text'].includes(formatType) && textColorState === true;
    },
}

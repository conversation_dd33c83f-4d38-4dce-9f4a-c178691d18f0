export default {
    type: 'field',
    name: 'dateColorRules',
    title: '日期颜色设置',
    display: 'accordion',
    setter: {
        componentName: 'ArraySetter',
        props: {
            itemSetter: {
                name: 'dateColorRules',
                title: '自定义规则',
                componentName: 'ObjectSetter',
                props: {
                    config: {
                        items: [
                            {
                                name: 'rule',
                                title: '对比规则',
                                display: 'inline',
                                isRequired: true,
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                title: '大于',
                                                value: '>'
                                            },
                                            {
                                                title: '小于',
                                                value: '<'
                                            },
                                            {
                                                title: '等于',
                                                value: '='
                                            },
                                        ]
                                    }
                                },
                            },
                            {
                                name: 'valueType',
                                title: '日期类型',
                                display: 'inline',
                                isRequired: true,
                                defaultValue: 'fixed',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                title: '固定日期',
                                                value: 'fixed',
                                            },
                                            {
                                                title: '动态日期',
                                                value: 'dynamic',
                                            },
                                        ],
                                    },
                                },
                                extraProps: {
                                    setValue: (target, value) => {
                                        target.parent.setPropValue('value', '');
                                    }
                                },
                            },
                            {
                                name: 'dateType',
                                title: '日期维度',
                                display: 'inline',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: {
                                        options: [
                                            {
                                                title: '年',
                                                value: 'year'
                                            },
                                            {
                                                title: '月',
                                                value: 'month'
                                            },
                                            {
                                                title: '日',
                                                value: 'day'
                                            },
                                        ]
                                    }
                                },
                                condition: (target) => {
                                    const valueType = target.parent.getPropValue('valueType');
                                    return valueType === 'dynamic';
                                }
                            },
                            {
                                name: 'value',
                                title: '对比日期',
                                display: 'inline',
                                setter: 'StringSetter',
                                condition: (target) => {
                                    const valueType = target.parent.getPropValue('valueType');
                                    return valueType === 'fixed';
                                }
                            },
                            {
                                name: 'value',
                                title: '对比日期',
                                setter: {
                                    componentName: 'SelectSetter',
                                    props: (target) => {
                                        const dateType = target.parent.getPropValue('dateType');
                                        let options: any[] = [];
                                        switch (dateType) {
                                            case 'year':
                                                options = [
                                                    {
                                                        title: '明年',
                                                        value: 'nextYear',
                                                    },
                                                    {
                                                        title: '今年',
                                                        value: 'nowYear',
                                                    },
                                                    {
                                                        title: '去年',
                                                        value: 'lastYear',
                                                    },
                                                ]
                                                break;
                                            case 'month':
                                                options = [
                                                    {
                                                        title: '下个月',
                                                        value: 'nextMonth',
                                                    },
                                                    {
                                                        title: '本月',
                                                        value: 'nowMonth',
                                                    },
                                                    {
                                                        title: '上个月',
                                                        value: 'lastMonth',
                                                    },
                                                ]
                                                break;
                                            case 'day':
                                                options = [
                                                    {
                                                        title: '明天',
                                                        value: 'tomorrow',
                                                    },
                                                    {
                                                        title: '今天',
                                                        value: 'today',
                                                    },
                                                    {
                                                        title: '昨天',
                                                        value: 'yesterday',
                                                    },
                                                ]
                                                break;
                                        }


                                        return {
                                            options,
                                        }
                                    },
                                },
                                condition: (target) => {
                                    const valueType = target.parent.getPropValue('valueType');
                                    return valueType === 'dynamic';
                                }
                            },
                            {
                                name: 'color',
                                title: '颜色',
                                display: 'inline',
                                setter: 'ColorSetter',
                            },
                        ]
                    }
                },
            },
            initialValue: () => {
                return {
                    rule: ''
                };
            },
        }
    },
    condition: (target) => {
        const type = target.parent.getPropValue('type');
        return type === 'date';
    },
}

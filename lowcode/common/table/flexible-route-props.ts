export const flexibleRouteProps = [
  {
    name: 'flexibleRoute',
    title: '自由跳转',
    display: 'inline',
    defaultValue: false,
    setter: 'BoolSetter',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const canShow = ['text', 'number', 'progress', 'merge']?.includes(type);
      return canShow;
    },
  },
  {
    name: 'linkConfig',
    title: '跳转配置',
    display: 'inline',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const typeCorrect = ['text', 'number', 'progress', 'merge']?.includes(type);
      const canShow = target.parent.getPropValue('flexibleRoute');
      return typeCorrect && canShow;
    },
    setter: 'LinkBoxSetter',
  },
  {
    name: 'ruleRouteControl',
    title: '规则触发跳转',
    display: 'inline',
    defaultValue: false,
    setter: 'BoolSetter',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const typeCorrect = ['text', 'number', 'progress', 'merge']?.includes(type);
      const canShow = target.parent.getPropValue('flexibleRoute');
      return typeCorrect && canShow;
    },
  },
  {
    name: 'ruleConfig',
    title: '规则配置',
    display: 'inline',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const typeCorrect = ['text', 'number', 'progress', 'merge']?.includes(type);
      const canShow =
        target.parent.getPropValue('flexibleRoute') &&
        target.parent.getPropValue('ruleRouteControl');
      return typeCorrect && canShow;
    },
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'rule',
                  title: '规则',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: (target) => {
                      const dataType = target.parent.parent.parent.getPropValue('type');

                      if (dataType === 'text') {
                        return {
                          options: [
                            {
                              title: '等于',
                              value: 'equal',
                            },
                            {
                              title: '包含',
                              value: 'include',
                            },
                          ],
                          showSearch: true,
                          allowClear: true,
                        };
                      }

                      return {
                        options: [
                          {
                            title: '大于',
                            value: '>',
                          },
                          {
                            title: '小于',
                            value: '<',
                          },
                          {
                            title: '等于',
                            value: '=',
                          },
                          {
                            title: '大于且等于',
                            value: '>=',
                          },
                          {
                            title: '小于且等于',
                            value: '<=',
                          },
                        ],
                        showSearch: true,
                        allowClear: true,
                      };
                    },
                  },
                },
                {
                  name: 'value',
                  title: '值',
                  display: 'inline',
                  isRequired: true,
                  setter: 'StringSetter',
                },
              ],
            },
          },
        },
        initialValue: () => {
          return {
            rule: '',
          };
        },
      },
    },
  },
];

export const flexibleRouteWithoutLinkProps = [
  {
    name: 'ruleRouteControl',
    title: '规则触发跳转',
    display: 'inline',
    defaultValue: false,
    setter: 'BoolSetter',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const typeCorrect = ['text', 'number', 'progress', 'merge']?.includes(type);
      return typeCorrect;
    },
  },
  {
    name: 'ruleConfig',
    title: '规则配置',
    display: 'inline',
    condition: (target) => {
      const type = target.parent.getPropValue('type');
      const typeCorrect = ['text', 'number', 'progress', 'merge']?.includes(type);
      const canShow = target.parent.getPropValue('ruleRouteControl');
      return typeCorrect && canShow;
    },
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'rule',
                  title: '规则',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: (target) => {
                      const dataType = target.parent.parent.parent.getPropValue('type');

                      if (dataType === 'text') {
                        return {
                          options: [
                            {
                              title: '等于',
                              value: 'equal',
                            },
                            {
                              title: '包含',
                              value: 'include',
                            },
                          ],
                          showSearch: true,
                          allowClear: true,
                        };
                      }

                      return {
                        options: [
                          {
                            title: '大于',
                            value: '>',
                          },
                          {
                            title: '小于',
                            value: '<',
                          },
                          {
                            title: '等于',
                            value: '=',
                          },
                          {
                            title: '大于且等于',
                            value: '>=',
                          },
                          {
                            title: '小于且等于',
                            value: '<=',
                          },
                        ],
                        showSearch: true,
                        allowClear: true,
                      };
                    },
                  },
                },
                {
                  name: 'value',
                  title: '值',
                  display: 'inline',
                  isRequired: true,
                  setter: 'StringSetter',
                },
              ],
            },
          },
        },
        initialValue: () => {
          return {
            rule: '',
          };
        },
      },
    },
  },
];

import _ from 'lodash';

// 数值颜色配置加对比配置 目前仅用于指标组、指标列表、指标组插槽、指标组2.0 4个组件
export const numberColorAndContrastProps = (type: number) => {
  // type 用于判断组件，根据组件处理不同对比下拉数据  0:指标组、指标列表、指标组插槽 1：指标组2.0
  return [
    {
      name: 'numberColorState',
      title: '数字状态颜色',
      setter: 'BoolSetter',
      condition: (target) => {
        const contrastState = target.parent.getPropValue('contrastState');
        return !contrastState;
      },
    },
    {
      name: 'colorType',
      title: '颜色类型',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              value: 'staticState',
              title: '静态',
            },
            {
              value: 'dynamic',
              title: '动态',
            },
          ],
        },
      },
      extraProps: {
        setValue: (target, value) => {
          const colorState = target.parent.getPropValue('numberColorState');
          if (value === 'dynamic' && colorState) {
            const colorRules = [
              {
                rule: '<',
                value: 0.9,
                color: '#F65B5B',
              },
              {
                rule: '>=',
                value: 0.9,
                color: '#FB8437',
              },
              {
                rule: '>=',
                value: 1,
                color: '#59C252',
              },
            ];
            target.parent.setPropValue('colorRules', colorRules);
          }
        },
      },
      condition: (target) => {
        return target.parent.getPropValue('numberColorState') === true;
      },
    },
    {
      name: 'color',
      title: '自定义颜色',
      setter: 'ColorSetter',
      condition: (target) => {
        return (
          target.parent.getPropValue('numberColorState') === true &&
          target.parent.getPropValue('colorType') === 'staticState'
        );
      },
    },
    {
      type: 'field',
      name: 'colorRules',
      title: '自定义规则',
      display: 'accordion',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'rule',
                    title: '规则',
                    display: 'inline',
                    isRequired: true,
                    setter: {
                      componentName: 'SelectSetter',
                      props: {
                        options: [
                          {
                            title: '大于',
                            value: '>',
                          },
                          {
                            title: '小于',
                            value: '<',
                          },
                          {
                            title: '等于',
                            value: '=',
                          },
                          {
                            title: '大于且等于',
                            value: '>=',
                          },
                          {
                            title: '小于且等于',
                            value: '<=',
                          },
                        ],
                      },
                    },
                  },
                  {
                    name: 'value',
                    title: '数值',
                    display: 'inline',
                    isRequired: true,
                    setter: {
                      componentName: 'NumberSetter',
                      props: {
                        precision: 3,
                      },
                    },
                  },
                  {
                    name: 'color',
                    title: '颜色',
                    display: 'inline',
                    setter: 'ColorSetter',
                  },
                ],
              },
            },
            initialValue: () => {
              return {
                rule: '',
              };
            },
          },
        },
      },
      condition: (target) => {
        return (
          target.parent.getPropValue('numberColorState') === true &&
          target.parent.getPropValue('colorType') === 'dynamic'
        );
      },
    },
    {
      name: 'contrastState',
      title: '是否对比',
      display: 'inline',
      initialValue: false,
      setter: 'BoolSetter',
      condition: (target) => {
        const numberColorState = target.parent.getPropValue('numberColorState');
        const layoutCorrect = !target.getProps().getPropValue('componentStyle.layout');
        return layoutCorrect && !numberColorState;
      },
    },
    {
      name: 'data',
      type: 'group',
      display: 'accordion',
      title: '对比配置',
      items: [
        {
          name: 'contrastKey',
          title: '对比字段',
          display: 'inline',
          setter: {
            componentName: 'SelectSetter',
            props: (target) => {
              if (type === 0) {
                const options = [];
                const tempList =
                  target.getProps().getPropValue('allDataColumnClone') ||
                  target.getProps().getPropValue('allDataColumn');
                const optionsList = Object.values(tempList);

                optionsList?.forEach((item) => {
                  item?.mainField &&
                    options.push({
                      title: item?.mainTitle,
                      value: item?.mainField,
                    });

                  if (item?.columns?.length) {
                    item?.columns?.forEach((_item) => {
                      _item?.key &&
                        options.push({
                          title: _item?.title || _item?.nickName,
                          value: _item?.key,
                        });
                    });
                  }
                });

                return {
                  options,
                  showSearch: true,
                };
              }

              if (type === 1) {
                const option = target.getProps().getPropValue('columnsOption');

                return {
                  options: option,
                  showSearch: true,
                };
              }

              return {
                options: [],
                showSearch: true,
              };
            },
          },
        },
        {
          name: 'largerValue',
          title: '较大值颜色',
          display: 'inline',
          defaultValue: '#F65B5B',
          setter: 'ColorSetter',
        },
        {
          name: 'smallerValue',
          title: '较小值颜色',
          display: 'inline',
          defaultValue: '#59C252',
          setter: 'ColorSetter',
        },
      ],
      condition: (target) => {
        const contrastState = target.parent.getPropValue('contrastState');
        return contrastState === true;
      },
    },
  ];
};

// 指标列表、指标组、指标组插槽 循环设置数值颜色跟对比配置 代码逻辑一样从而进行统一，方便维护；
export const setPropsByIndicatorGroup = (i) => {
  return [
    {
      name: `allDataColumn.${i}.numberColorState`,
      title: '数字状态颜色',
      setter: 'BoolSetter',
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const contrastState = target.getProps().getPropValue(`allDataColumn.${i}.contrastState`);
        return !!mainField && !contrastState;
      },
    },
    {
      name: `allDataColumn.${i}.colorType`,
      title: '颜色类型',
      setter: {
        componentName: 'RadioGroupSetter',
        props: {
          options: [
            {
              value: 'staticState',
              title: '静态',
            },
            {
              value: 'dynamic',
              title: '动态',
            },
          ],
        },
      },
      extraProps: {
        setValue: (target, value) => {
          const colorState = target.getProps().getPropValue(`allDataColumn.${i}.numberColorState`);
          if (value === 'dynamic' && colorState) {
            const colorRules = [
              {
                rule: '<',
                value: 0.9,
                color: '#F65B5B',
              },
              {
                rule: '>=',
                value: 0.9,
                color: '#FB8437',
              },
              {
                rule: '>=',
                value: 1,
                color: '#59C252',
              },
            ];
            target.getProps().setPropValue(`allDataColumn.${i}.colorRules`, colorRules);
          }
        },
      },
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const colorState = target.getProps().getPropValue(`allDataColumn.${i}.numberColorState`);
        return !!mainField && colorState === true;
      },
    },
    {
      name: `allDataColumn.${i}.color`,
      title: '自定义颜色',
      setter: 'ColorSetter',
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const colorState = target.getProps().getPropValue(`allDataColumn.${i}.numberColorState`);
        const colorType = target.getProps().getPropValue(`allDataColumn.${i}.colorType`);
        return !!mainField && colorState === true && colorType === 'staticState';
      },
    },
    {
      type: 'field',
      name: `allDataColumn.${i}.colorRules`,
      title: '自定义规则',
      display: 'accordion',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'rule',
                    title: '规则',
                    display: 'inline',
                    isRequired: true,
                    setter: {
                      componentName: 'SelectSetter',
                      props: {
                        options: [
                          {
                            title: '大于',
                            value: '>',
                          },
                          {
                            title: '小于',
                            value: '<',
                          },
                          {
                            title: '等于',
                            value: '=',
                          },
                          {
                            title: '大于且等于',
                            value: '>=',
                          },
                          {
                            title: '小于且等于',
                            value: '<=',
                          },
                        ],
                      },
                    },
                  },
                  {
                    name: 'value',
                    title: '数值',
                    display: 'inline',
                    isRequired: true,
                    setter: {
                      componentName: 'NumberSetter',
                      props: {
                        precision: 3,
                      },
                    },
                  },
                  {
                    name: 'color',
                    title: '颜色',
                    display: 'inline',
                    setter: 'ColorSetter',
                  },
                ],
              },
            },
            initialValue: () => {
              return {
                rule: '',
              };
            },
          },
        },
      },
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const colorState = target.getProps().getPropValue(`allDataColumn.${i}.numberColorState`);
        const colorType = target.getProps().getPropValue(`allDataColumn.${i}.colorType`);
        return !!mainField && colorState === true && colorType === 'dynamic';
      },
    },
    {
      name: `allDataColumn.${i}.contrastState`,
      title: '是否对比',
      display: 'inline',
      initialValue: false,
      setter: 'BoolSetter',
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const layoutCorrect = !target.getProps().getPropValue('componentStyle.layout');
        const numberColorState = target
          .getProps()
          .getPropValue(`allDataColumn.${i}.numberColorState`);
        return !!mainField && layoutCorrect && !numberColorState;
      },
    },
    {
      name: 'data',
      type: 'group',
      display: 'accordion',
      title: '对比配置',
      items: [
        {
          name: `allDataColumn.${i}.contrastKey`,
          title: '对比字段',
          display: 'inline',
          setter: {
            componentName: 'SelectSetter',
            props: (target) => {
              const options = [];
              const tempList =
                target.getProps().getPropValue('allDataColumnClone') ||
                target.getProps().getPropValue('allDataColumn');
              const optionsList = Object.values(tempList);

              optionsList?.forEach((item) => {
                item?.mainField &&
                  options.push({
                    title: item?.mainTitle,
                    value: item?.mainField,
                  });

                if (item?.columns?.length) {
                  item?.columns?.forEach((_item) => {
                    _item?.key &&
                      options.push({
                        title: _item?.title || _item?.nickName,
                        value: _item?.key,
                      });
                  });
                }
              });

              return {
                options,
                showSearch: true,
              };
            },
          },
        },
        {
          name: `allDataColumn.${i}.largerValue`,
          title: '较大值颜色',
          display: 'inline',
          defaultValue: '#F65B5B',
          setter: 'ColorSetter',
        },
        {
          name: `allDataColumn.${i}.smallerValue`,
          title: '较小值颜色',
          display: 'inline',
          defaultValue: '#59C252',
          setter: 'ColorSetter',
        },
      ],
      condition: (target) => {
        const mainField = target.getProps().getPropValue(`allDataColumn.${i}.mainField`);
        const numberColorState = target
          .getProps()
          .getPropValue(`allDataColumn.${i}.numberColorState`);
        const contrastState = target.getProps().getPropValue(`allDataColumn.${i}.contrastState`);
        return !!mainField && !numberColorState && contrastState === true;
      },
    },
  ];
};

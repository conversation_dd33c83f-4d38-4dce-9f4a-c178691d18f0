/*
 * @Author: kiki
 * @Date: 2024-06-17 18:36:45
 * @LastEditTime: 2024-06-25 11:41:09
 * @LastEditors: kiki
 * @Description:
 */
interface IconProps {
    fieldPrefix?: string;
    oldField?: string;
    verifyField?: string;
    fieldPath?: string; // relative：相对路径，absolute：绝对路径
}

export const indicatorTitleIconProps = ({
    fieldPrefix,
    oldField,
    verifyField,
    fieldPath = 'absolute',
}: IconProps) => {
    // name属性前缀【相对路径为空】
    const namePrefix =
        fieldPrefix && fieldPath === 'absolute'
            ? `${fieldPrefix}.`
            : '';
    // 全局属性前缀：获取/设置属性【使用绝对路径】
    const globalPrefix = fieldPrefix ? `${fieldPrefix}.` : '';

    return [{
        name: `${namePrefix}iconShowType`,
        title: '主指标图标',
        defaultValue: '',
        display: 'inline',
        setter: {
            componentName: 'RadioGroupSetter',
            props: {
                options: [{
                    value: 'icon',
                    title: '选择icon',
                }, {
                    value: 'img',
                    title: '选择图片',
                }],
            },
            initialValue: (target) => {
                const oldFieldVal = target.getProps().getPropValue(`${globalPrefix}${oldField}`);
                if (oldFieldVal) {
                    target.getProps().setPropValue(`${globalPrefix}iconUrl`, oldFieldVal);
                    return 'icon';
                }
                return '';
            },
        },
        extraProps: {
            condition: (target) => {
                if (!verifyField) {
                    return true;
                } else {
                    const verifyVal = target.getProps().getPropValue(`${globalPrefix}${verifyField}`);
                    return !!verifyVal;
                }
            },
        },
    }, {
        name: `${namePrefix}iconUrl`,
        title: { label: '' },
        display: 'inline',
        condition: (target) => {
            const type = target.getProps().getPropValue(`${globalPrefix}iconShowType`);
            return type === 'icon';
        },
        setter: 'IconFontSetter',
    }, {
        name: `${namePrefix}iconImgUrl`,
        title: { label: '' },
        defaultValue: '',
        display: 'inline',
        condition: (target) => {
            const type = target.getProps().getPropValue(`${globalPrefix}iconShowType`);
            return type === 'img';
        },
        setter: 'JNImgSelectSetter',
    }]
}

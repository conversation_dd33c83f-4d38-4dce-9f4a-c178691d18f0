import dayjs from 'dayjs';
import { get } from 'lodash';
import { getFilterArray } from '../../../src/utils';

export const generateDefaultDateFilterRange = () => {
  const currTimeStamp = dayjs(new Date()).unix() - 86400;
  const beforeSevenDayTimeStamp = currTimeStamp - 7 * 86400; // 7天时间戳
  const currDate = dayjs.unix(currTimeStamp).format('YYYY-MM-DD');
  const beforeSevenDayDate = dayjs.unix(beforeSevenDayTimeStamp).format('YYYY-MM-DD');
  const defaultFiltersDate = get(window, 'pageConfig.timeRange.value') || [
    beforeSevenDayDate,
    currDate,
  ];

  return defaultFiltersDate;
};

export const getFilterId = () => {
  return get(window, 'pageConfig.filterId') || !window.onClient && get(window, 'parent.pageConfig.filterId') || '';
};

export const generateCommonPayload = (datasetId: string, extraConfig?: Record<string, any>) => {
  return {
    filters: getFilterArray(),
    id: datasetId,
    limit: 1,
    ...extraConfig,
  };
};

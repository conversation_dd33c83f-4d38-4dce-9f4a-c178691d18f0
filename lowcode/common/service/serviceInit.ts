import { ceil, get } from 'lodash';
import { requestBjxDataset } from '../../service/chart';

let headListSelectOption:any = {} // 获取最新的数据集headList

export const getSelectOption = (bindDataset) => {
  if(bindDataset && headListSelectOption[bindDataset]){
    return headListSelectOption[bindDataset]
  }else if(!bindDataset){
    return []
  }else{
    requestBjxDataset(bindDataset).then(data => {
      headListSelectOption[bindDataset] = get(data, 'data.result.data.headList');

    })
  }
}

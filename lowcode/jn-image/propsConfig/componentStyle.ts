import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const componentStyleItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'componentStyle.titleControl',
    title: '标题显示',
    setter: 'BoolSetter',
    extraProps: {
      defaultValue: true,
      display: 'inline',
    },
  },
  // {
  //   type: 'field',
  //   name: 'componentStyle.route',
  //   title: '跳转链接',
  //   setter: 'StringSetter',
  //   extraProps: {
  //     display: 'inline',
  //     condition: (target) => {
  //       return !!target.getProps().getPropValue('componentStyle.routeControl');
  //     },
  //   },
  // },
  // {
  //   type: 'field',
  //   name: 'componentStyle.routeControl',
  //   title: '跳转开关',
  //   setter: 'BoolSetter',
  //   extraProps: {
  //     defaultValue: false,
  //     display: 'inline',
  //   },
  // },
];

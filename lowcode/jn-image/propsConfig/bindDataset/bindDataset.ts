import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const bindDatasetItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'indicatorGroupDataset.bindDataset',
    title: '数据绑定',
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        hasClear: true,
        showSearch: true,
        followTrigger: true,
        placeholder: '请选择数据集',
        filterType: 'Object',
      },
    },
    extraProps: {
      display: 'inline',
    },
  },
];

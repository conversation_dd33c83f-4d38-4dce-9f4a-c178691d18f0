import { get } from 'lodash';
import { requestBjxDataset } from '../../../service/chart';

export const initComponentConfig = (target, value, action = 'set') => {
  if (value) {
    switch (action) {
      case 'get': {
        requestBjxDataset(value)
          .then((res) => {
            // 初始化
            target.parent.setPropValue('allHeadList', []);

            // 数据字段列表
            const headList = get(res, 'data.result.data.headList');
            const optionList = headList?.map((item) => {
              return {
                title: item?.title,
                value: item?.key,
              };
            });

            target.parent.setPropValue(`allHeadList`, optionList);
          })
          .catch((err) => {
            console.error('[LowCode-SetValue]数据集处理异常', err);
          });

        break;
      }
      case 'set': {
        requestBjxDataset(value)
          .then((res) => {
            // 初始化
            target.parent.setPropValue('allHeadList', []);

            // 数据字段列表
            const headList = get(res, 'data.result.data.headList');
            const optionList = headList?.map((item) => {
              return {
                title: item?.title,
                value: item?.key,
              };
            });

            target.parent.setPropValue(`allHeadList`, optionList);
          })
          .catch((err) => {
            console.error('[LowCode-SetValue]数据集处理异常', err);
          });

        break;
      }
      default:
    }
  }
};

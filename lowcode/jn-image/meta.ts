import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNImageSnippets } from './snippets';
import { JNImageProps } from './props';

const JNImageMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNImage',
  group: 'MelGeek组件',
  category: '卡片',
  title: '单图卡片',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b037.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNImage',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNImageProps,
    component: {},
  },
  snippets: JNImageSnippets,
};

export default JNImageMeta;

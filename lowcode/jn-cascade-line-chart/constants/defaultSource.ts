export const DEFAULT_SOURCE = {
  series: [
    {
      name: '销售金额',
      key: 'IdrrViORud',
      data: [
        {
          y: 8722239.4139,
          colorBy: '',
        },
        {
          y: 9234485.7297,
          colorBy: '',
        },
        {
          y: 106488422.2828,
          colorBy: '',
        },
        {
          y: 56332447.94,
          colorBy: '',
        },
        {
          y: 32986716.1043,
          colorBy: '',
        },
        {
          y: 35797390.0937,
          colorBy: '',
        },
        {
          y: 16721384.8775,
          colorBy: '',
        },
      ],
      format: {
        specifier: ',.0f',
        suffix: '',
        isAuto: false,
        excelFormat: '#,###0""',
      },
      yAxis: 0,
      metric_additional: false,
      value: '0',
      numberValue: 0,
    },
    {
      name: '同比',
      key: 'EZgzfGJqdx',
      data: [
        {
          y: 0.637939,
          colorBy: '',
        },
        {
          y: 0.606887,
          colorBy: '',
        },
        {
          y: 0.356509,
          colorBy: '',
        },
        {
          y: 0.454604,
          colorBy: '',
        },
        {
          y: 0.801676,
          colorBy: '',
        },
        {
          y: 0.556481,
          colorBy: '',
        },
        {
          y: 0.865847,
          colorBy: '',
        },
      ],
      format: {
        specifier: ',.1%',
        suffix: '',
        divideDataBy: 1,
        isAuto: false,
        decimalPlaces: 1,
        excelFormat: '#,###0.0%""',
      },
      yAxis: 1,
      metric_additional: true,
      value: '0.0%',
      numberValue: 0,
    },
  ],
  serieColor: ['#489cff', '#8BD12E', '#fb8437', '#FFC300', '#997DED'],
  categories: [
    '2023-05-30',
    '2023-05-31',
    '2023-05-01',
    '2023-06-02',
    '2023-06-03',
    '2023-06-04',
    '2023-06-05',
  ],
  activityDays: [],
  componentStyle: {
    chartTitle: '销售金额趋势',
    chartTitleShow: true,
    borderShow: true,
  },
  dataColumn: {
    dataColumn: {
      XAxis: {
        labelForAxis: true,
        dataType: 'text',
      },
      YAxis: {
        labelForAxis: true,
        lineStyle: 'solid',
        labelForNumeric: true,
        labelForNumericChoice: 'maxAndMin',
        nameShowSize: 'maxAndMin',
        lineFlagShow: true,
      },
    },
  },
  specificDataset: {
    bindDataset: [49],
    datasetOption: [
      {
        title: '付款毛利率_趋势图',
        value: 48,
      },
      {
        title: '30天可用成本周转_趋势',
        value: 51,
      },
      {
        title: '销售金额_趋势图',
        value: 49,
      },
      {
        title: '付款毛利率_趋势图',
        value: 50,
      },
      {
        title: '退款率(追溯)_趋势',
        value: 46,
      },
      {
        title: '会员GMV贡献率_趋势',
        value: 47,
      },
      {
        title: '初上市价折扣率_趋势',
        value: 43,
      },
      {
        title: '客单价_趋势',
        value: 44,
      },
      {
        title: '差评率_趋势',
        value: 45,
      },
      {
        title: '好评率_趋势',
        value: 41,
      },
      {
        title: '差评率_趋势',
        value: 42,
      },
      {
        title: '销售金额_趋势图',
        value: 11,
      },
    ],
  },
  defaultChartOptions: {
    xAxis: {
      type: 'category',
      data: [
        '2023-06-04',
        '2023-06-05',
        '2023-06-06',
        '2023-06-07',
        '2023-06-08',
        '2023-06-09',
        '2023-06-10',
        '2023-06-11',
        '2023-06-12',
        '2023-06-13',
        '2023-06-14',
        '2023-06-15',
        '2023-06-16',
        '2023-06-17',
        '2023-06-18',
        '2023-06-19',
        '2023-06-20',
        '2023-06-21',
        '2023-06-22',
        '2023-06-23',
        '2023-06-24',
        '2023-06-25',
        '2023-06-26',
        '2023-06-27',
        '2023-06-28',
        '2023-06-29',
        '2023-06-30',
        '2023-07-01',
        '2023-07-02',
        '2023-07-03',
      ],
      show: true,
      axisLine: {
        lineStyle: {
          color: '#DDE0EA',
        },
        onZero: true,
      },
      axisLabel: {
        color: '#86909C',
      },
    },
    yAxis: [
      {
        type: 'value',
        scale: true,
        show: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#EDEFF5',
            type: 'dashed',
          },
        },
        axisLabel: {},
      },
      {
        type: 'value',
        scale: true,
        show: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#EDEFF5',
            type: 'dashed',
          },
        },
        axisLabel: {},
      },
    ],
    grid: {
      left: '1%',
      right: '1%',
      bottom: '1%',
      containLabel: true,
    },
    color: ['#489cff', '#8BD12E', '#fb8437', '#FFC300', '#997DED'],
    textStyle: {
      fontFamily:
        'number-mr, font-fz, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#489cff',
          width: 2,
          dashOffset: [4, 5],
        },
        z: 1,
      },
      padding: 0,
      className: 'echart-tooltip-content',
    },
    legend: {
      icon: 'circle',
      height: 32,
      itemHeight: 10,
      itemWidth: 10,
      textStyle: {
        padding: [0, 40, 0, 0],
        lineHeight: 31,
        height: 30,
        rich: {},
      },
    },
    series: [
      {
        type: 'line',
        scale: true,
        show: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#EDEFF5',
            type: 'dashed',
          },
        },
        axisLabel: {},
        name: '销售金额',
        smooth: true,
        symbolSize: 12,
        showSymbol: false,
        z: 2,
        emphasis: {
          scale: false,
        },
        yAxisIndex: 0,
        lineStyle: {
          width: 3,
          cap: 'round',
          type: 'solid',
        },
        format: {
          specifier: ',.0f',
          suffix: '',
          divideDataBy: null,
          isAuto: false,
          decimalPlaces: null,
          excelFormat: '#,###0""',
        },
        markPoint: {
          z: 3,
          data: [
            {
              type: 'max',
              name: 'Max',
              symbol: 'emptycircle',
              symbolSize: [12, 12],
              itemStyle: {
                borderWidth: 10,
              },
              label: {
                show: true,
                offset: [0, -24],
                padding: [4, 10, 4, 10],
                borderRadius: 4,
                lineHeight: 18,
                color: '#ffffff',
                backgroundColor: 'inherit',
              },
            },
          ],
        },
        // markArea: {
        //   itemStyle: {
        //     color: {
        //       type: 'linear',
        //       x: 1,
        //       y: 1,
        //       x2: 1,
        //       y2: 0,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: '#DBF0FF',
        //         },
        //         {
        //           offset: 0.99,
        //           color: 'rgba(193,229,255,0.00)',
        //         },
        //       ],
        //       global: false,
        //     },
        //   },
        //   label: {
        //     position: 'insideBottomLeft',
        //     offset: [10, -10],
        //     color: '#489CFF',
        //   },
        //   data: [],
        //   emphasis: {
        //     disabled: true,
        //   },
        //   z: 0,
        // },
        data: [
          16668172.8, 16791404.9299, 15497942.35, 15230239.0099, 18866163.16, 18931131.4599,
          16994689.88, 17081526.8999, 14962723.16, 16128249.86, 10330399.6601, 49669513.1004,
          30196548.0598, 27197902.7399, 45539098.78, 19444828.09, 28573597.77, 8791973.8899,
          9295122.9299, 9611168.7899, 9707645.7703, 9454112.76, 10096421.5001, 10597282.0726,
          10041009.2161, 9995447.1247, 10287877.1744, 13031395.6636, 11291438.4687, 9536236.9832,
        ],
      },
      {
        type: 'line',
        scale: true,
        show: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#EDEFF5',
            type: 'dashed',
          },
        },
        axisLabel: {},
        name: '同比',
        smooth: true,
        symbolSize: 12,
        showSymbol: false,
        z: 2,
        emphasis: {
          scale: false,
        },
        yAxisIndex: 1,
        lineStyle: {
          width: 3,
          cap: 'round',
          type: 'solid',
        },
        format: {
          specifier: ',.1%',
          suffix: '',
          divideDataBy: true,
          isAuto: false,
          decimalPlaces: 1,
          excelFormat: '#,###0.0%""',
        },
        markPoint: {
          z: 3,
          data: [
            {
              type: 'max',
              name: 'Max',
              symbol: 'emptycircle',
              symbolSize: [12, 12],
              itemStyle: {
                borderWidth: 10,
              },
              label: {
                show: true,
                offset: [0, -24],
                padding: [4, 10, 4, 10],
                borderRadius: 4,
                lineHeight: 18,
                color: '#ffffff',
                backgroundColor: 'inherit',
              },
            },
          ],
        },
        // markArea: {
        //   itemStyle: {
        //     color: {
        //       type: 'linear',
        //       x: 1,
        //       y: 1,
        //       x2: 1,
        //       y2: 0,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: '#DBF0FF',
        //         },
        //         {
        //           offset: 0.99,
        //           color: 'rgba(193,229,255,0.00)',
        //         },
        //       ],
        //       global: false,
        //     },
        //   },
        //   label: {
        //     position: 'insideBottomLeft',
        //     offset: [10, -10],
        //     color: '#489CFF',
        //   },
        //   data: [],
        //   emphasis: {
        //     disabled: true,
        //   },
        //   z: 0,
        // },
        data: [
          0.859909, 0.788604, 0.8387, 0.270175, 0.744717, 1.010547, 1.677198, 1.086297, 0.911155,
          1.958457, 1.357889, 0.481757, 0.457004, 0.677672, 0.508508, 0.138113, 0.589943, 0.460454,
          0.56575, 0.629928, 0.858377, 0.482065, 0.460059, 0.68442, 0.791597, 0.739697, 0.857857,
          1.645861, 0.86262, 0.354875,
        ],
      },
    ],
  },
};

import { DEFAULT_SOURCE } from './constants/defaultSource';
import props from './props';

const JNCascadeLineChartSnippets = [
  {
    title: '联动折线图',
    screenshot:
      'https://img.alicdn.com/imgextra/i2/O1CN01ChN5mm1txOQnh6kTh_!!6000000005968-55-tps-56-56.svg',
    schema: {
      componentName: 'JNCascadeLineChart',
      props: DEFAULT_SOURCE,
    },
  },
];

const JNCascadeLineChartMeta = {
  componentName: 'JNCascadeLineChart',
  title: '联动折线图',
  group: '默认分组',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNCascadeLineChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNCascadeLineChartSnippets,
};

export default { ...JNCascadeLineChartMeta, JNCascadeLineChartSnippets };

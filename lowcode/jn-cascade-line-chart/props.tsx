import { componentStyleItems } from './propsConfig/componentStyle';
import { dataColumnItems } from './propsConfig/dataColumn';
import { advancedItems } from './propsConfig/advanced';

const props = [
  // 组件样式
  {
    name: 'componentStyle',
    type: 'group',
    display: 'accordion',
    title: {
      label: '组件样式',
    },
    items: componentStyleItems,
  },
  // 数据列
  {
    name: 'dataColumn',
    type: 'field',
    extraProps: {
      display: 'accordion',
    },
    title: {
      label: '数据列',
    },
    setter: dataColumnItems,
  },
  // 高级
  // {
  //   name: 'advanced',
  //   type: 'field',
  //   display: 'accordion',
  //   title: {
  //     label: '高级',
  //   },
  //   setter: advancedItems,
  // },
  // 样式
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'plain',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              isShowWidthHeight: false,
            },
          },
        },
      },
    ],
  },
];

export default props;

export const auxiliaryLineItems = {
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'dataField',
              title: '数据字段',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: 'nickName',
              title: '数据名称',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: `lineName`,
              title: '线名称',
              display: 'inline',
              isRequired: true,
              setter: {
                componentName: 'StringSetter',
                props: {
                  placeholder: '线名称',
                },
              },
            },
            {
              name: `lineValueType`,
              title: '线值类型',
              display: 'inline',
              isRequired: true,
              defaultValue: 'average',
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: [
                    {
                      title: '固定值',
                      value: 'fixed',
                    },
                    {
                      title: '平均值',
                      value: 'average',
                    },
                    {
                      title: '中位数',
                      value: 'median',
                    },
                    {
                      title: '最大值',
                      value: 'max',
                    },
                    {
                      title: '最小值',
                      value: 'min',
                    },
                  ],
                },
              },
            },
            {
              name: `lineStyle`,
              title: '线样式',
              display: 'inline',
              isRequired: true,
              defaultValue: 'dashed',
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: [
                    {
                      title: '粗实线',
                      value: 'solid',
                    },
                    {
                      title: '大虚线',
                      value: 'dashed',
                    },
                    {
                      title: '小虚线',
                      value: 'dotted',
                    },
                  ],
                },
              },
            },
          ],
        },
      },
    },
  },
};

export const dataColumnItems = {
  componentName: 'ObjectSetter',
  props: {
    config: {
      items: [
        {
          name: 'dataColumn.XAxis',
          title: 'X轴',
        },
        {
          name: 'dataColumn.XAxis.dataType',
          title: '数据类型',
          defaultValue: 'text',
          display: 'inline',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                { value: 'text', title: '文本' },
                { value: 'date', title: '日期' },
              ],
            },
          },
        },
        {
          name: 'dataColumn.XAxis.labelForAxis',
          title: '轴标签',
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'dataColumn.XAxis.labelForAxisFormat',
          title: '格式',
          setter: {
            componentName: 'SelectSetter',
            props: {
              options: [
                {
                  value: 'YYYY/MM/DD',
                  title: 'YYYY/MM/DD',
                },
                {
                  value: 'YY/MM/DD',
                  title: 'YY/MM/DD',
                },
                {
                  value: 'YYYY/MM',
                  title: 'YYYY/MM',
                },
                {
                  value: 'MM/DD',
                  title: 'MM/DD',
                },
                {
                  value: 'YYYY年MM月DD日',
                  title: 'YYYY年MM月DD日',
                },
              ],
            },
          },
          condition: (target) => {
            const dataType = target.parent.getPropValue('dataColumn.XAxis.dataType');
            return dataType === 'date';
          },
        },
        {
          name: 'dataColumn.YAxis',
          title: 'Y轴',
        },
        // {
        {
          name: 'dataColumn.YAxis.labelForAxis',
          title: {
            label: '轴标签',
          },
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'dataColumn.YAxis.labelForNumeric',
          title: {
            label: '图像标签',
          },
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'dataColumn.YAxis.labelForNumericChoice',
          title: {
            label: '数值',
          },
          defaultValue: 'maxAndMin',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '最大/最小',
                  value: 'maxAndMin',
                },
                {
                  title: '线初',
                  value: 'start',
                },
                {
                  title: '线尾',
                  value: 'recent',
                },
                {
                  title: '全部',
                  value: 'all',
                },
                {
                  title: '不显示',
                  value: '',
                },
              ],
            },
          },
          condition: (target) => {
            const canShowNumeric = target.parent.getPropValue('dataColumn.YAxis.labelForNumeric');
            return !!canShowNumeric;
          },
        },
        {
          name: 'dataColumn.YAxis.nameShowSize',
          title: '数值名称',
          condition: (target) => {
            const canShowNumeric = target.parent.getPropValue('dataColumn.YAxis.labelForNumeric');
            return !!canShowNumeric;
          },
          defaultValue: 'maxAndMin',
          setter: {
            componentName: 'RadioGroupSetter',
            props: {
              options: [
                {
                  title: '最大/最小',
                  value: 'maxAndMin',
                },
                {
                  title: '线初',
                  value: 'start',
                },
                {
                  title: '线尾',
                  value: 'recent',
                },
                {
                  title: '全部',
                  value: 'all',
                },
                {
                  title: '不显示',
                  value: '',
                },
              ],
            },
          },
        },
        {
          name: 'dataColumn.YAxis.lineStyle',
          title: '线样式',
          setter: {
            componentName: 'SelectSetter',
            initialValue: 'solid',
            props: {
              options: [
                {
                  label: '粗实线',
                  value: 'solid',
                },
                {
                  label: '大虚线',
                  value: 'dashed',
                },
                {
                  label: '小点线',
                  value: 'dotted',
                },
              ],
            },
          },
        },
        // {
        //   name: 'dataColumn.YAxis.lineFlagShow',
        //   title: '线名称展示',
        //   defaultValue: false,
        //   setter: 'BoolSetter',
        // },
      ],
    },
  },
};

import props from './props';

const J<PERSON>ommonContainerSnippets = {
  title: '对比容器',
  style: {
    marginTop: '12px',
    padding: '16px',
    backgroundColor: 'pink',
  },
  content: {
    type: 'JSSlot',
    value: [
      {
        componentName: 'JNSubFilterBar',
        id: 'node_oclwuctb1k2',
        props: {
          styleType: '通用',
          filters: [
            {
              selectorType: 'multipleSelect',
              width: 200,
              type: 'l68a633bafaca4990a3706d7',
              defalutKey: '',
              filterType: '',
              filterId: 4445,
              label: '品类',
              tiemFrame: ['昨天', '上周', '本月至昨天', '日', '周', '月', '年', '自定义'],
              defalutType: 'fixedValue',
              multiple: true,
              show: true,
            },
          ],
        },
        hidden: false,
        title: '',
        isLocked: false,
        condition: true,
        conditionGroup: '',
      },
      {
        componentName: 'JNTableOfInter',
        id: 'node_oclwuctb1k3',
        props: {
          dataSource: [],
          data: [
            {
              year: '1991',
              value: 72345678,
            },
            {
              year: '1992',
              value: 4321132,
            },
            {
              year: '1993',
              value: 33121112.5,
            },
            {
              year: '1994',
              value: 45227221,
            },
            {
              year: '1995',
              value: 4321221.9,
            },
            {
              year: '1996',
              value: 6322121,
            },
            {
              year: '1997',
              value: 78312213,
            },
            {
              year: '1998',
              value: 4192312,
            },
            {
              year: '1999',
              value: 6212332,
            },
            {
              year: '2000',
              value: 3192312,
            },
          ],
          border: 1,
          id: '',
          settingButtons: true,
          columns: [],
          autoDiff: true,
          componentStyle: {
            layoutControl: false,
            defaultLayout: 'compact',
          },
          tooltipConfig: {
            tooltipType: 'simpleText',
          },
          actionColumnProps: {
            actionColumnTitle: '操作',
          },
          zebraCrossingType: 'row',
          renderIndex: 1,
          tableHeight: 500,
          pageSize: 10,
          showType: 'dropDownOption',
          positioning: {
            title: '自定义表头',
          },
        },
        hidden: false,
        title: '',
        isLocked: false,
        condition: true,
        conditionGroup: '',
      },
    ],
    id: 'jn_common_a001',
  },
  moreOpen: false,
  linkShowText: '更多分析',
};

const JNCommonContainerDiffMeta = [
  {
    componentName: 'JNCommonContainer',
    title: '对比容器',
    category: '布局容器类',
    group: 'MelGeek组件',
    docUrl: '',
    devMode: 'proCode',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
    npm: {
      package: 'jiaoneiui',
      version: '{{version}}',
      exportName: 'JNCommonContainer',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },
    configure: {
      component: {
        isContainer: false,
        nestingRule: {},
      },
      props,
      // supports: {
      //   events: ['onSave', 'onRemove'],
      // },
    },
    snippets: [
      {
        title: '对比容器',
        screenshot: 'https://cdn-h5.bananain.cn/icons/b028.png',
        schema: {
          componentName: 'JNCommonContainer',
          props: JNCommonContainerSnippets,
        },
      },
    ],
  },
];

export default JNCommonContainerDiffMeta;

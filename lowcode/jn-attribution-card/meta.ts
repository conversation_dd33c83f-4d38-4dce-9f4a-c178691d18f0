import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNAttributionCardSnippets } from './snippets';
import { JNAttributionCardProps } from './props';

const JNAttributionCardMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNAttributionCard',
  group: 'MelGeek组件',
  category: '卡片',
  title: '小蕉诊断',
  icon: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNAttributionCard',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNAttributionCardProps,
    component: {},
  },
  snippets: JNAttributionCardSnippets,
};

export default JNAttributionCardMeta;

import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { bindDatasetItems, componentStyleItems, bindIndicatorsItem } from './propsConfig';
import { annotationItems } from './propsConfig/annotation';

export const JNAttributionCardProps: IPublicTypeFieldConfig[] = [
  // 绑定数据集
  {
    type: 'group',
    name: 'bindDataset',
    title: '绑定数据集',
    items: bindDatasetItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 绑定指标
  {
    type: 'group',
    name: 'bindIndicators',
    title: '绑定指标',
    items: bindIndicatorsItem,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局
  {
    type: 'group',
    name: 'componentStyle',
    title: '组件样式',
    items: componentStyleItems,
    extraProps: {
      display: 'accordion',
    },
  },
  {
    type: 'group',
    name: 'annotation',
    title: '注释',
    items: annotationItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局样式
  {
    type: 'group',
    name: 'style',
    title: '组件全局样式',
    items: [
      {
        type: 'field',
        name: 'style',
        title: '组件全局样式',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
        extraProps: {
          display: 'plain',
        },
      },
    ],
    extraProps: {
      display: 'accordion',
    },
  },
];

import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const componentStyleItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'componentStyle.title',
    title: '标题',
    setter: 'StringSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'componentStyle.route',
    title: '跳转',
    setter: 'BoolSetter',
    extraProps: {
      display: 'inline',
      defaultValue: false,
    },
  },
];

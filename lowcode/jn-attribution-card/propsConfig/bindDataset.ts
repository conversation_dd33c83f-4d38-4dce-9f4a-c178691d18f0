import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { handleBjxFieldsSet, handleIndicatorsSet } from '../service';

export const bindDatasetItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'bindDataset',
    title: '选择数据集',
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        allowClear: true,
        showSearch: true,
        followTrigger: true,
        placeholder: '请选择数据集',
        filterType: 'ObjectList',
      },
    },
    extraProps: {
      display: 'inline',
      setValue: (target, value) => {
        if (value) {
          handleBjxFieldsSet(target, value);
          handleIndicatorsSet(target);
        }
      },
    },
  },
];

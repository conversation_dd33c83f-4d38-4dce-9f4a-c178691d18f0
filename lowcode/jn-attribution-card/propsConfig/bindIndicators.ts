import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const bindIndicatorsItem: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'bindIndicators',
    title: '绑定指标',
    setter: {
      componentName: 'ArraySetter',
      props: {
        itemSetter: {
          componentName: 'ObjectSetter',
          props: {
            config: {
              items: [
                {
                  name: 'dataField',
                  title: '选择字段',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: (target) => {
                      const bjxFields = target.getProps().getPropValue('bjxFieldsOption');
                      if (bjxFields?.length) {
                        return {
                          allowClear: true,
                          showSearch: true,
                          placeholder: '请选择字段',
                          options: bjxFields,
                        };
                      }

                      return {
                        allowClear: true,
                        showSearch: true,
                        placeholder: '请选择字段',
                        options: [],
                      };
                    },
                  },
                },
                {
                  name: 'indicatorField',
                  title: '绑定指标',
                  display: 'inline',
                  isRequired: true,
                  setter: {
                    componentName: 'SelectSetter',
                    props: (target) => {
                      const indicatorsList = target.getProps().getPropValue('indicatorsOption');
                      if (indicatorsList?.length) {
                        return {
                          allowClear: true,
                          showSearch: true,
                          placeholder: '请选择字段',
                          options: indicatorsList,
                        };
                      }

                      return {
                        allowClear: true,
                        showSearch: true,
                        placeholder: '请选择字段',
                        options: [],
                      };
                    },
                  },
                },
                {
                  type: 'field',
                  name: 'routeMode',
                  title: '跳转方向',
                  condition: (target) => {
                    return !!target.getProps().getPropValue('componentStyle.route');
                  },
                  setter: {
                    componentName: 'RadioGroupSetter',
                    props: {
                      options: [
                        { label: '诊断', value: 'analyze' },
                        { label: '链接', value: 'link' },
                      ],
                    },
                    initialValue: 'analyze',
                  },
                  extraProps: {
                    display: 'inline',
                  },
                },
                {
                  name: 'bindFilterId',
                  title: '诊断关联筛选器ID',
                  display: 'inline',
                  condition: (target) => {
                    const isAnalyze = target.parent.getPropValue('routeMode') === 'analyze';
                    return isAnalyze;
                  },
                  setter: 'StringSetter',
                },
                {
                  name: 'linkConfig',
                  title: '跳转配置',
                  display: 'inline',
                  condition: (target) => {
                    const isCommon = target.parent.getPropValue('routeMode') === 'link';
                    return isCommon;
                  },
                  setter: 'LinkBoxSetter',
                },
              ],
            },
          },
        },
      },
    },
  },
];

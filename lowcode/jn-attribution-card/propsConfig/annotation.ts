import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const annotationItems: IPublicTypeFieldConfig[] = [
  {
    type: 'field',
    name: 'annotation.control',
    title: '注释',
    defaultValue: false,
    setter: 'BoolSetter',
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'annotation.title',
    title: '标题',
    defaultValue: '小蕉诊断',
    setter: 'StringSetter',
    condition: (target) => {
      return !!target.getProps().getPropValue('annotation.control');
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'field',
    name: 'annotation.mainComment',
    title: '描述区域',
    setter: 'TextAreaSetter',
    condition: (target) => {
      return !!target.getProps().getPropValue('annotation.control');
    },
    extraProps: {
      display: 'inline',
    },
  },
  {
    type: 'group',
    name: 'annotation.subComment',
    title: '说明区域',
    items: [
      {
        type: 'field',
        name: 'annotation.subComment.labelFirst',
        title: '标题名称-1',
        setter: 'StringSetter',
        extraProps: {
          display: 'inline',
        },
      },
      {
        type: 'field',
        name: 'annotation.subComment.commentFirst',
        title: '说明-1',
        setter: 'TextAreaSetter',
        extraProps: {
          display: 'inline',
        },
      },
      {
        type: 'field',
        name: 'annotation.subComment.labelSecond',
        title: '标题名称-2',
        setter: 'StringSetter',
        extraProps: {
          display: 'inline',
        },
      },
      {
        type: 'field',
        name: 'annotation.subComment.commentSecond',
        title: '说明-2',
        setter: 'TextAreaSetter',
        extraProps: {
          display: 'inline',
        },
      },
    ],
    condition: (target) => {
      return !!target.getProps().getPropValue('annotation.control');
    },
    extraProps: {
      display: 'accordion',
    },
  },
];

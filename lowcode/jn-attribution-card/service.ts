import { requestIndicators } from '../service/indicator';
import { requestBjxDataset } from '../service/polaris';

export const handleBjxFieldsSet = async (target, bindDataset) => {
  const bjxFieldsResponse = await requestBjxDataset(bindDataset);
  const bjxFieldsList = bjxFieldsResponse?.data?.result?.data?.headList;
  const nextBjxFieldsList = bjxFieldsList?.map((item) => {
    return {
      label: item?.title,
      value: item?.key,
      fieldId: item?.fieldId,
      assistants: item?.assistants,
    };
  });
  target.getProps().setPropValue('bjxFieldsOption', nextBjxFieldsList);
};

export const handleIndicatorsSet = async (target) => {
  const indicatorsResponse = await requestIndicators();
  const indicatorsList = indicatorsResponse?.data?.result?.list;
  const nextIndicatorList = indicatorsList?.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
  target.getProps().setPropValue('indicatorsOption', nextIndicatorList);
};

import { leftCardProps, rightCardProps } from './defaultValue'; // 默认数据

const JNCardBoxProps = [
  {
    name: 'data',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'title',
        title: '标题',
        setter: 'StringSetter',
      },
      {
        name: 'iconType',
        title: '图标',
        setter: 'IconFontSetter',
      },
      {
        name: 'defaultOpen',
        title: '默认展开',
        setter: 'BoolSetter',
      },
    ],
  },
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'line',
        setter: 'StyleSetter',
      },
    ],
  },
];

export const cardBoxItemMeta = {
  componentName: 'CardBoxItem',
  title: '卡片容器',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b027.png',
  devMode: 'proCode',
  category: '布局容器类',
  group: 'MelGeek组件',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'CardBoxItem',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: [
      {
        name: 'content',
        title: '容器内容',
        setter: 'SlotSetter', // 可支持拖拽组件或可支持外部传入组件，此项需配置为 SlotSetter
      },
      {
        name: 'style',
        title: '样式',
        display: 'accordion',
        setter: {
          componentName: 'StyleSetter',
        },
      },
    ],

    component: {
      isContainer: false,
      // disableBehaviors: '*',
      nestingRule: {},
    },
  },
  snippets: [
    {
      title: '卡片容器',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b027.png',
      schema: {
        componentName: 'CardBoxItem',
        props: {
          ids: '', // 可能有
          content: {
            type: 'JSSlot', // 可支持拖拽组件或可支持外部传入组件，此项需配置为 JSSlot
            id: 'jn_card_a001',
          },
        },
      },
    },
  ],
};

export const JNCardMeta = [
  {
    componentName: 'ComparedCardBox',
    title: '指标卡页签',
    docUrl: '',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b014.png',
    devMode: 'procode',
    group: 'MelGeek组件',
    category: '卡片',
    tags: ['MelGeek组件'],
    npm: {
      package: 'jiaoneiui',
      version: '0.0.1',
      exportName: 'ComparedCardBox',
      main: 'lib/index.js',
      destructuring: true,
      subName: '',
    },

    configure: {
      props: JNCardBoxProps,

      component: {
        isContainer: true,
        // disableBehaviors: '*',
        nestingRule: {
          childWhitelist: ['ComparedCard', 'CardBoxItem', 'ChildForm'],
        },
      },
      supports: {
        events: ['onSave', 'onRemove'],
      },
    },
    snippets: [
      {
        title: '指标卡页签',
        screenshot: 'https://cdn-h5.bananain.cn/icons/b014.png',
        schema: {
          componentName: 'ComparedCardBox',
          props: {
            ids: '', // 可能有
            title: '渠道表现',
            // iconType: 'icon-a-dianshang',
            style: {
              position: 'relative',
              width: '100%',
              borderRadius: '16px',
              backgroundColor: 'rgb(255, 255, 255)',
              overflow: 'auto',
              padding: '16px',
              marginTop: '12px',
            },
          },
          children: [
            {
              componentName: 'CardBoxItem',
              props: {
                dataSource: [],

                content: {
                  type: 'JSSlot',
                  value: [
                    {
                      componentName: 'JNObjectCard',
                      props: leftCardProps,
                      hidden: false,
                      title: '',
                      isLocked: false,
                      condition: true,
                      conditionGroup: '',
                    },
                  ],
                  title: '插槽容器',
                },
              },
            },
            {
              componentName: 'CardBoxItem',
              props: {
                dataSource: [],
                content: {
                  type: 'JSSlot',
                  value: [
                    {
                      componentName: 'JNObjectCard',
                      props: rightCardProps,
                      hidden: false,
                      title: '',
                      isLocked: false,
                      condition: true,
                      conditionGroup: '',
                    },
                  ],
                  title: '插槽容器',
                },
              },
            },
            {
              componentName: 'CardBoxItem',
              props: {
                style: {
                  height: '100%',
                },
                content: {
                  type: 'JSSlot',
                  value: [
                    {
                      componentName: 'ComparedTable',
                    },
                  ],
                  title: '插槽容器',
                },
              },
            },
            {
              componentName: 'CardBoxItem',
              props: {
                style: {
                  height: '100%',
                },
                content: {
                  type: 'JSSlot',
                  value: [
                    {
                      componentName: 'ComparedTable',
                    },
                  ],
                  title: '插槽容器',
                },
              },
            },
          ],
        },
      },
    ],
  },

  cardBoxItemMeta,
];

export default JNCardMeta;

const dataBuild = (data) => {
    const colorList = [
        {
            color: '#489CFF',
            textColor: '#0D4C98',
        },
        {
            color: '#59C252',
            textColor: '#0F690A',
        },
        {
            color: '#FB8437',
            textColor: '#7B3407',
        },
        {
            color: '#FFD600',
            textColor: '#816301',
        },
        {
            color: '#997DED',
            textColor: '#4121A1',
        },
        {
            color: '#6DB0FF',
            textColor: '#175AA8',
        },
        {
            color: '#F65B5B',
            textColor: '#800606',
        },
        {
            color: '#CD8252',
            textColor: '#6A2C05',
        },
        {
            color: '#D1A4FF',
            textColor: '#4F1887',
        },
        {
            color: '#00C5B8',
            textColor: '#005D56',
        },
    ];
    const defaultData = {
        // name: '',
        // nameOptions: [],
        // number: '',
        // numberOptions: [],
        option: [],
        columns: [],
    };

    // if (data && data.length) {
    //     data.forEach((item, i) => {
    //         if (item?.fieldMeta?.metaType === 'DIM') {
    //             defaultData.name = item?.key;
    //             defaultData.nameOptions.push({
    //                 title: item?.title,
    //                 value: item?.key,
    //             });
    //         } else {
    //             defaultData.numberOptions.push({
    //                 title: item?.title,
    //                 value: item?.key,
    //             });
    //         }
    //     })

    //     defaultData.number = defaultData.numberOptions[0]?.value;
    // }

    // if (dataList && dataList.length) {
    //     dataList.map((item, i) => {
    //         defaultData.columns.push({
    //             name: item[data[0]?.key],
    //             color: i > 9 ? colorList[i % 10].color : colorList[i].color,
    //             textColor: i > 9 ? colorList[i % 10].textColor : colorList[i].textColor,
    //             defalutShow: true,
    //         })
    //     })
    // }

    if (data && data?.length) {
        data.forEach((item, i) => {

            defaultData.option.push({
                title: item?.title,
                value: item?.key,
            });

            defaultData.columns.push({
                name: item.title,
                key: item.key,
                color: i > 9 ? colorList[i % 10].color : colorList[i].color,
                textColor: i > 9 ? colorList[i % 10].textColor : colorList[i].textColor,
                defalutShow: true,
            })
        })
    }

    return defaultData;
}

export default dataBuild;
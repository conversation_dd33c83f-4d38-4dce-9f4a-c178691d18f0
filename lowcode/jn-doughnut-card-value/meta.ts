// =========================蕉内 甜甜圈指标卡组件
import props from './propsConfig/props';

export const JNDoughnutCardValue = {
  componentName: 'JNDoughnutCardValue',
  title: '甜甜圈-无维度',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b020.png',
  group: 'MelGeek组件',
  category: '卡片',
  tags: ['MelGeek组件'],
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNDoughnutCardValue',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props,
    component: {
      isContainer: false,
      nestingRule: {},
    },
    // supports: {
    //   events: ['onSave', 'onRemove'],
    // },
  },
  snippets: [
    {
      title: '甜甜圈-无维度',
      screenshot: 'https://cdn-h5.bananain.cn/icons/b020.png',
      schema: {
        componentName: 'JNDoughnutCardValue',
        props: {
          style: {
            width: '400px',
            height: '400px',
          },
        },
      },
    },
  ],
};

export default JNDoughnutCardValue;

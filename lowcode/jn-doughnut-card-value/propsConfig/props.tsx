import { getPolarisDataById } from "../../../src/services/cards/cardDataService";
import dataBuild from '../utils';
import dataColumnItems from './dataColumns';
import linkConfig from '../../common/linkConfig';
import tag from "./tag";
import legend from "./legend";
import { getFilterArray } from '../../../src/utils';

export default [
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '绑定数据集',
        items: [
            {
                name: 'id',
                title: '数据ID',
                extraProps: {
                    display: 'line',
                    setValue: (target, value) => {
                        if (value) {
                            target.parent.setPropValue('dataList', {
                                columns: [],
                                option: [],
                            });
                            target.parent.setPropValue('allDrillCardIds', []);

                            getPolarisDataById({
                                id: value,
                                data: {
                                    filterValue: getFilterArray()
                                },
                            }).then(data => {
                                const delData = dataBuild(data?.data?.headList);

                                target.parent.setPropValue('allDrillCardIds', data?.data?.allDrillCardIds || []);
                                target.parent.setPropValue('dataList.columns', delData.columns);
                                target.parent.setPropValue('dataList.option', delData.option);
                                target.parent.setPropValue('id', value);
                            })
                        }
                    }
                },
                setter: [
                    {
                        componentName: 'JNSelectSetter',
                        props: {
                            showSearch: true,
                            filterType: 'Object',
                        }
                    },]
            }
        ],
    },
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '组件样式',
        items: [
            {
                name: 'componentStyle.titleShow',
                title: '标题显示',
                defaultValue: true,
                setter: 'BoolSetter',
            },
            {
                name: 'componentStyle.title',
                title: '标题',
                setter: 'StringSetter',
                condition: (target) => {
                    return target.getProps().getPropValue('componentStyle.titleShow') || false;
                },
            },
            {
                name: 'componentStyle.iconType',
                title: '图标',
                defaultValue: '',
                setter: 'IconFontSetter',
                condition: (target) => {
                    return target.getProps().getPropValue('componentStyle.titleShow') || false;
                },
            },
            //  跳转
            ...linkConfig,
        ]
    },
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '数据列',
        items: [
            {
                name: 'dataList.columns',
                display: 'inline',
                title: '数据',
                setter: dataColumnItems,
            },
        ],

    },
    {
        name: 'data',
        type: 'group',
        display: 'accordion',
        title: '甜甜圈',
        items: [
            {
                name: 'doughnut.inner',
                title: '内半径(%)',
                defaultValue: '65',
                setter: 'StringSetter',
            },
            {
                name: 'doughnut.out',
                title: '外半径(%)',
                defaultValue: '80',
                setter: 'StringSetter',
            },
            {
                name: 'doughnut.top',
                title: '上下间距(%)',
                defaultValue: '50',
                setter: 'StringSetter',
            },
            {
                name: 'doughnut.left',
                title: '左右间距(%)',
                defaultValue: '50',
                setter: 'StringSetter',
            },
        ]
    },
    tag,
    legend,
    {
        name: "style",
        title: "样式",
        display: 'accordion',
        setter: {
            componentName: 'StyleSetter',
        },
    },
]
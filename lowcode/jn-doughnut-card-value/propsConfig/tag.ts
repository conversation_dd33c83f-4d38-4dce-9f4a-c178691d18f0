const tag = {
    name: 'data',
    type: 'group',
    display: 'accordion',
    title: '标签',
    items: [
        {
            name: 'tag.show',
            title: '显示标签',
            defaultValue: true,
            setter: 'BoolSetter',
        },
        {
            name: 'tag.position',
            title: '位置',
            setter: {
                componentName: 'RadioGroupSetter',
                initialValue: 'inner',
                props: {
                    options: [
                        { title: '内部', value: 'inner' },
                        { title: '外部', value: 'out' },
                    ],
                },
            },
            condition: (target) => {
                return target.getProps().getPropValue('tag.show') || false;
            },
        },
        {
            name: 'tag.content',
            title: '内容',
            setter: {
                componentName: 'SelectSetter',
                initialValue: ['name', 'value'],
                props: {
                    options: [
                        {
                            title: '名称',
                            value: 'name',
                        },
                        {
                            title: '数值',
                            value: 'value',
                        },
                        {
                            title: '百分比',
                            value: 'percent',
                        },
                    ],
                    mode: 'multiple',
                }
            },
            condition: (target) => {
                return target.getProps().getPropValue('tag.show') || false;
            },
        },
        {
            name: 'tag.percentPrecision',
            title: '百分比精确度',
            setter: 'NumberSetter',
            defaultValue: 1,
            condition: (target) => {
                const show = target.getProps().getPropValue('tag.show');
                const content = target.getProps().getPropValue('tag.content') || [];
                return show === true && content.includes('percent');
            }
        },
        {
            name: 'tag.centerLabel',
            title: '中心标签',
            defaultValue: false,
            setter: 'BoolSetter',
        },
        {
            name: 'tag.titleTop',
            title: '距离Top(%)',
            defaultValue: '40',
            setter: 'StringSetter',
            condition: (target) => {
                return target.getProps().getPropValue('tag.centerLabel') || false;
            },
        },
        {
            name: 'tag.titleLeft',
            title: '距离Left(%)',
            defaultValue: '50',
            setter: 'StringSetter',
            condition: (target) => {
                return target.getProps().getPropValue('tag.centerLabel') || false;
            },
        },
        {
            name: 'tag.centerLabelType',
            title: ' ',
            setter: {
                componentName: 'RadioGroupSetter',
                initialValue: 'showTotal',
                props: {
                    options: [
                        { title: '显示总和', value: 'showTotal' },
                        { title: '自定义文本', value: 'customText' },
                    ],
                },
            },
            condition: (target) => {
                return target.getProps().getPropValue('tag.centerLabel') || false;
            },
        },
        {
            name: 'tag.lableTitle',
            title: '标签名称',
            defaultValue: '总和',
            setter: 'StringSetter',
            condition: (target) => {
                return target.getProps().getPropValue('tag.centerLabel') &&
                    target.getProps().getPropValue('tag.centerLabelType') === 'showTotal' || false;
            },
        },
        {
            name: 'tag.text',
            title: '文本',
            defaultValue: '默认文本',
            setter: 'StringSetter',
            condition: (target) => {
                return target.getProps().getPropValue('tag.centerLabel') &&
                    target.getProps().getPropValue('tag.centerLabelType') === 'customText' || false;
            },
        },
    ],
}

export default tag;
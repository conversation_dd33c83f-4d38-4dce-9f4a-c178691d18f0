const legend = {
    name: 'data',
    type: 'group',
    display: 'accordion',
    title: '图例',
    items: [
        {
            name: 'legend.show',
            title: '显示图例',
            defaultValue: '',
            setter: 'BoolSetter',
            extraProps: {
                setValue: (target, value) => {
                    if (value === true) {
                        const legendpOsition = target.getProps().getPropValue('legend.position');
                        switch (legendpOsition) {
                            case 'top':
                                target.getProps().setPropValue('tag.titleTop', '50');
                                target.getProps().setPropValue('tag.titleLeft', '50');
                                target.getProps().setPropValue('doughnut.top', '60');
                                target.getProps().setPropValue('doughnut.left', '50');
                                break;
                            case 'left':
                                target.getProps().setPropValue('tag.titleTop', '40');
                                target.getProps().setPropValue('tag.titleLeft', '69');
                                target.getProps().setPropValue('doughnut.top', '50');
                                target.getProps().setPropValue('doughnut.left', '70');
                                break;
                            case 'right':
                                target.getProps().setPropValue('tag.titleTop', '40');
                                target.getProps().setPropValue('tag.titleLeft', '29');
                                target.getProps().setPropValue('doughnut.top', '50');
                                target.getProps().setPropValue('doughnut.left', '30');
                                break;
                            default:
                                break;
                        }
                    } else {
                        target.getProps().setPropValue('tag.titleTop', '40');
                        target.getProps().setPropValue('tag.titleLeft', '50');
                        target.getProps().setPropValue('doughnut.top', '50');
                        target.getProps().setPropValue('doughnut.left', '50');
                    }
                    target.getProps().setPropValue('legend.show', value);
                }
            },
        },
        {
            name: 'legend.position',
            title: '位置',
            extraProps: {
                setValue: (target, value) => {
                    if (value) {
                        target.getProps().setPropValue('legend.position', value);
                        const legendShow = target.getProps().getPropValue('legend.show');
                        if (legendShow === true) {
                            switch (value) {
                                case 'top':
                                    target.getProps().setPropValue('tag.titleTop', '50');
                                    target.getProps().setPropValue('tag.titleLeft', '50');
                                    target.getProps().setPropValue('doughnut.top', '60');
                                    target.getProps().setPropValue('doughnut.left', '50');
                                    break;
                                case 'left':
                                    target.getProps().setPropValue('tag.titleTop', '40');
                                    target.getProps().setPropValue('tag.titleLeft', '69');
                                    target.getProps().setPropValue('doughnut.top', '50');
                                    target.getProps().setPropValue('doughnut.left', '70');
                                    break;
                                case 'right':
                                    target.getProps().setPropValue('tag.titleTop', '40');
                                    target.getProps().setPropValue('tag.titleLeft', '29');
                                    target.getProps().setPropValue('doughnut.top', '50');
                                    target.getProps().setPropValue('doughnut.left', '30');
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            },
            setter: {
                componentName: 'RadioGroupSetter',
                initialValue: 'top',
                props: {
                    options: [
                        { title: '顶部', value: 'top' },
                        { title: '左侧', value: 'left' },
                        { title: '右侧', value: 'right' },
                    ],
                },
            },
            condition: (target) => {
                return target.getProps().getPropValue('legend.show') || false;
            },
        },
        {
            name: 'legend.marginTop',
            title: '距离Top(%)',
            defaultValue: '5',
            setter: 'StringSetter',
            condition: (target) => {
                return target.getProps().getPropValue('legend.show') || false;
            },
        },
        {
            name: 'legend.content',
            title: '内容',
            setter: {
                componentName: 'SelectSetter',
                initialValue: ['name'],
                props: {
                    options: [
                        {
                            title: '名称',
                            value: 'name',
                        },
                        {
                            title: '数值',
                            value: 'value',
                        },
                    ],
                    mode: 'multiple',
                }
            },
            condition: (target) => {
                return target.getProps().getPropValue('legend.show') || false;
            },
        },
    ]
}

export default legend;
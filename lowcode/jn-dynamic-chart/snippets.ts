import { IPublicTypeSnippet } from '@alilc/lowcode-types';

export const J<PERSON>ynamicChartSnippets: IPublicTypeSnippet[] = [
  {
    title: '动态对比图',
    screenshot: 'https://cdn-h5.bananain.cn/icons/b024.png',
    schema: {
      componentName: 'JNDynamicChart',
      props: {
        componentStyle: {
          chartTitleShow: true,
          chartTitle: '动态对比图',
          showMoreText: '更多分析',
          legendShow: true,
          dataZoom: false,
        },
        xDataColumnItems: {
          labelForAxis: true,
        },
        style: {
          marginTop: 20,
          backgroundColor: '#ffffff',
        },
      },
    },
  },
];

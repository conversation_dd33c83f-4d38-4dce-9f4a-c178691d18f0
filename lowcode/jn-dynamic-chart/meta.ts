import { IPublicTypeComponentMetadata } from '@alilc/lowcode-types';
import { JNDynamicChartSnippets } from './snippets';
import { JNDynamicChartProps } from './props';

const JNDynamicChartMeta: IPublicTypeComponentMetadata = {
  componentName: 'JNDynamicChart',
  group: 'MelGeek组件',
  category: '图表类',
  title: '动态对比图',
  icon: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b024.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '{{version}}',
    exportName: 'JNDynamicChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    props: JNDynamicChartProps,
    component: {},
  },
  snippets: JNDynamicChartSnippets,
};

export default JNDynamicChartMeta;

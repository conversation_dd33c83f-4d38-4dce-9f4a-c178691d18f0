import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const componentStyleItems: IPublicTypeFieldConfig[] = [
  {
    name: 'componentStyle.chartTitleShow',
    title: '标题显示',
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.chartTitle',
    title: '图表名称',
    setter: 'StringSetter',
  },
  {
    name: 'componentStyle.showMoreText',
    title: '更多显示',
    setter: {
      componentName: 'MixedSetter',
      props: {
        setters: ['StringSetter', { componentName: 'IconFontSetter', props: { type: 'node' } }],
      },
    },
  },
  {
    name: 'componentStyle.linkText',
    title: '链接地址',
    setter: 'StringSetter',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.showMoreText');
    },
  },
  {
    name: 'componentStyle.legendShow',
    title: '图例展示',
    defaultValue: true,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.chartHeight',
    title: '图表高度',
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.barWidth',
    title: '柱状宽度',
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.smooth',
    title: '平滑折线',
    defaultValue: true,
    setter: {
      componentName: 'BoolSetter',
    },
  },
  {
    name: 'componentStyle.chartGridTop',
    title: '图表上边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.chartGridBottom',
    title: '图表下边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.chartGridLeft',
    title: '图表左边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.chartGridRight',
    title: '图表右边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.dataZoom',
    title: '缩略组件',
    defaultValue: false,
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.zoomSite',
    title: '缩放范围',
    defaultValue: 'all',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.dataZoom');
    },
    setter: {
      componentName: 'SelectSetter',
      props: {
        initialValue: 'none',
        options: [
          {
            title: '全部',
            value: 'all',
          },
          {
            title: '左侧',
            value: 'left',
          },
          {
            title: '中心',
            value: 'center',
          },
          {
            title: '右侧',
            value: 'right',
          },
          {
            title: '自定义',
            value: 'custom',
          },
        ],
        mode: 'single',
        showSearch: true,
        hasClear: true,
      },
    },
  },
  {
    name: 'componentStyle.zoomStart',
    title: '缩放起点',
    condition: (target) => {
      const dataZoom = !!target.getProps().getPropValue('componentStyle.dataZoom');
      const zoomSiteCustom = target.getProps().getPropValue('componentStyle.zoomSite') === 'custom';
      return dataZoom && zoomSiteCustom;
    },
    defaultValue: 0,
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.zoomEnd',
    title: '缩放终点',
    condition: (target) => {
      const dataZoom = !!target.getProps().getPropValue('componentStyle.dataZoom');
      const zoomSiteCustom = target.getProps().getPropValue('componentStyle.zoomSite') === 'custom';
      return dataZoom && zoomSiteCustom;
    },
    defaultValue: 100,
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.nullConnect',
    title: 'NULL连接',
    defaultValue: false,
    setter: 'BoolSetter',
  }
];

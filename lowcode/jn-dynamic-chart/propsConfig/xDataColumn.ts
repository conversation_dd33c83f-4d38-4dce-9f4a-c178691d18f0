import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const xDataColumnItems: IPublicTypeFieldConfig[] = [
  {
    name: 'xDataColumn.labelForAxis',
    title: '轴标签',
    defaultValue: true,
    setter: 'BoolSetter',
  },
  {
    name: 'xDataColumn.dataType',
    title: '数据类型',
    defaultValue: 'text',
    display: 'inline',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          { value: 'text', title: '文本' },
          { value: 'number', title: '数值' },
          { value: 'date', title: '日期' },
        ],
      },
    },
  },
  {
    name: 'xDataColumn.labelForAxisFormat',
    title: '格式',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            value: 'YYYY/MM/DD',
            title: 'YYYY/MM/DD',
          },
          {
            value: 'YY/MM/DD',
            title: 'YY/MM/DD',
          },
          {
            value: 'YYYY/MM',
            title: 'YYYY/MM',
          },
          {
            value: 'MM/DD',
            title: 'MM/DD',
          },
          {
            value: 'YYYY年MM月DD日',
            title: 'YYYY年MM月DD日',
          },
        ],
      },
    },
    condition: (target) => {
      const dataType = target.getProps().getPropValue('xDataColumn.dataType');
      return dataType === 'date';
    },
  },
];

import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const yDataColumnItems: IPublicTypeFieldConfig[] = [
  {
    name: 'yDataColumn.leftChartType',
    title: '主轴类型',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            value: 'line',
            title: '折线图',
          },
          {
            value: 'bar',
            title: '柱状图',
          },
        ],
      },
    },
  },
  {
    name: 'yDataColumn.rightChartType',
    title: '次轴类型',
    setter: {
      componentName: 'SelectSetter',
      props: {
        options: [
          {
            value: 'line',
            title: '折线图',
          },
          {
            value: 'bar',
            title: '柱状图',
          },
        ],
      },
    },
  },
];

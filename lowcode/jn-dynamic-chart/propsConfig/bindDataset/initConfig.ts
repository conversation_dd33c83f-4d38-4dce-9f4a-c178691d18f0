import { get } from 'lodash';
import { requestBjxDataset } from '../../../service/chart';

export const initComponentConfig = (target, value, action = 'set') => {
  if (value) {
    switch (action) {
      case 'get': {
        requestBjxDataset(value)
          .then((res) => {
            const flatToData = get(res, 'data.result.data');
            console.log('[LowCode-GetValue-Data]', flatToData)
          })
          .catch((err) => {
            console.error('[LowCode-SetValue]数据集处理异常', err);
          });

        break;
      }
      case 'set': {
        requestBjxDataset(value)
          .then((res) => {
            const flatToData = get(res, 'data.result.data');
            console.log('[LowCode-SetValue-Data]', flatToData)
          })
          .catch((err) => {
            console.error('[LowCode-SetValue]数据集处理异常', err);
          });

        break;
      }
      default:
    }
  }
};

import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { bindDatasetItems } from './propsConfig/bindDataset/bindDataset';
import { componentStyleItems } from './propsConfig/componentStyle';
import { advancedItems } from './propsConfig/advanced';
import { xDataColumnItems } from './propsConfig/xDataColumn';
import { yDataColumnItems } from './propsConfig/yDataColumn';

export const JNDynamicChartProps: IPublicTypeFieldConfig[] = [
  // 绑定数据集
  {
    type: 'group',
    name: 'specificDataset',
    title: '数据集配置',
    items: bindDatasetItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 组件全局
  {
    type: 'group',
    name: 'componentStyle',
    title: '组件样式',
    items: componentStyleItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // X轴数据列
  {
    type: 'group',
    name: 'xDataColumn',
    title: 'X轴数据列',
    items: xDataColumnItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // Y轴数据列
  {
    type: 'group',
    name: 'yDataColumn',
    title: 'Y轴数据列',
    items: yDataColumnItems,
    extraProps: {
      display: 'accordion',
    },
  },
  // 高级
  {
    name: 'advanced',
    type: 'field',
    display: 'accordion',
    title: {
      label: '高级',
    },
    setter: advancedItems,
  },
  // 组件全局样式
  {
    type: 'group',
    name: 'style',
    title: '组件全局样式',
    items: [
      {
        type: 'field',
        name: 'style',
        title: '组件全局样式',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
        extraProps: {
          display: 'plain',
        },
      },
    ],
    extraProps: {
      display: 'accordion',
    },
  },
];

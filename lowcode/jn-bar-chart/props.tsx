import { bindDatasetItems } from './propsConfig/bindDataset';
import { componentStyleItems } from './propsConfig/componentStyle';
import { categoriesColumnItems } from './propsConfig/categoriesColumn';
import { seriesColumnItems } from './propsConfig/seriesColumn';
import { advancedItems } from './propsConfig/advanced';

const props = [
  // 绑定数据集
  {
    name: 'specificDataset',
    type: 'group',
    display: 'accordion',
    title: {
      label: '绑定数据集',
    },
    items: bindDatasetItems,
  },
  // 组件样式
  {
    name: 'componentStyle',
    type: 'group',
    display: 'accordion',
    title: {
      label: '组件样式',
    },
    items: componentStyleItems,
  },
  // 品类轴数据列
  {
    name: 'categoriesColumn',
    type: 'field',
    extraProps: {
      display: 'accordion',
    },
    title: {
      label: '品类轴数据列',
    },
    setter: categoriesColumnItems,
  },
  // 数据轴数据列
  {
    name: 'seriesColumn',
    type: 'field',
    extraProps: {
      display: 'accordion',
    },
    title: {
      label: '数据轴数据列',
    },
    setter: seriesColumnItems,
  },
  // 高级
  {
    name: 'advanced',
    type: 'field',
    display: 'accordion',
    title: {
      label: '高级',
    },
    setter: advancedItems,
  },
  // 全局组件样式
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '数据',
    },
    items: [
      {
        name: 'style',
        display: 'plain',
        setter: {
          componentName: 'StyleSetter',
          props: {
            showModuleList: ['layout', 'background', 'border'],
            layoutPropsConfig: {
              showDisPlayList: ['block', 'inline-block', 'none'],
              // isShowWidthHeight: false,
            },
          },
        },
      },
    ],
  },
];

export default props;

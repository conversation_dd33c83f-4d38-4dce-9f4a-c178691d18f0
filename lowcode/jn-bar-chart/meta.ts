import props from './props';
import { JNBarChartSnippets } from './snippets';

const JNBarChartMeta = {
  componentName: 'JNBarChart',
  title: '通用柱状图',
  category: '图表类',
  group: 'MelGeek组件',
  docUrl: '',
  screenshot: 'https://cdn-h5.bananain.cn/icons/b018.png',
  devMode: 'proCode',
  npm: {
    package: 'jiaoneiui',
    version: '0.0.1',
    exportName: 'JNBarChart',
    main: 'lib/index.js',
    destructuring: true,
    subName: '',
  },
  configure: {
    component: {
      isContainer: false,
      nestingRule: {},
    },
    props,
  },
  snippets: JNBarChartSnippets,
};

export default { ...JNBarChartMeta, JNBarChartSnippets };

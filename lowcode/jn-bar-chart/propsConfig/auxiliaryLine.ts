/**
 * 辅助线配置
 * 通用折线图， 通用柱状图， 通用混合图
 */

import _ from "lodash";
import { filterColumns } from "../../common/table/utils";
import { getSelectOption } from "../service/serviceInit";
import { ChartSelectOption } from "../../types";

export const auxiliaryLineItems = {
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'dataIndex',
              title: '数据字段',
              display: 'inline',
              defaultValue:  (target) =>   target.parent.getPropValue('dataField'),
              extraProps: {
                setValue: (target, value, other) => {
                  const options = target.getProps().getPropValue('options');

                  const selectColumn = _.find(options, { value: `${value}` })
                  target.parent.setPropValue('dataField', value);
                  target.parent.setPropValue('nickName', selectColumn?.label);
                  target.parent.setPropValue('lineName', selectColumn?.label);
                }
              },

              setter: {
                componentName: 'SelectSetter',
                props: (target) => {
                  const headList = target.getProps().getPropValue('seriesColumn.series.chartColumn');
                  const columns = headList?.map(item => {
                    return {
                      title: item.nickName,
                      key: item.dataField,
                    }
                  })

                  const option:ChartSelectOption[] = [];
                  if (columns && columns.length) {
                    columns.forEach(item => {
                      option.push(...filterColumns(item));
                    })
                  }
                  target.getProps().setPropValue('options', option);
                  return {
                    options: option,
                    showSearch: true,
                  }
                }
              },
            },
            {
              name: 'dataField',
              title: '数据字段',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: 'nickName',
              title: '数据名称',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: `lineName`,
              title: '线名称',
              display: 'inline',
              isRequired: true,
              setter: {
                componentName: 'StringSetter',
                props: {
                  placeholder: '线名称',
                },
              },
            },
            {
              name: `lineValueType`,
              title: '线值类型',
              display: 'inline',
              isRequired: true,
              defaultValue: 'average',
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: [
                    {
                      title: '固定值',
                      value: 'fixed',
                    },
                    {
                      title: '平均值',
                      value: 'average',
                    },
                    {
                      title: '中位数',
                      value: 'median',
                    },
                    {
                      title: '最大值',
                      value: 'max',
                    },
                    {
                      title: '最小值',
                      value: 'min',
                    },
                  ],
                },
              },
            },
            {
              name: `lineValue`,
              title: '线值',
              display: 'inline',
              isRequired: false,
              setter: {
                componentName: 'NumberSetter',
                props:{
                  precision: 2
                }
              },
              condition: (target) => {
                const lineValueType = target.parent.getPropValue('lineValueType') === 'fixed';
                console.log(lineValueType)
                return  lineValueType
              }
            },
            {
              name: `lineStyle`,
              title: '线样式',
              display: 'inline',
              isRequired: true,
              defaultValue: 'dashed',
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: [
                    {
                      title: '粗实线',
                      value: 'solid',
                    },
                    {
                      title: '大虚线',
                      value: 'dashed',
                    },
                    {
                      title: '小虚线',
                      value: 'dotted',
                    },
                  ],
                },
              },
            },
          ],
        },
      },
    },
  },
};

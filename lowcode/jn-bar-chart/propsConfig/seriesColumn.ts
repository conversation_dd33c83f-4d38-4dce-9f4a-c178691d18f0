import { chartColumnItems } from '../service/serviceHelper';

export const seriesColumnItems = {
  componentName: 'ObjectSetter',
  props: {
    config: {
      items: [
        {
          name: 'series.stackShow',
          title: '堆叠',
          defaultValue: false,
          setter: 'BoolSetter',
        },
        {
          name: 'series.labelForAxis',
          title: {
            label: '轴标签',
          },
          setter: 'BoolSetter',
          defaultValue: true,
        },
        {
          name: 'series.axisPosition',
          title: {
            label: '轴位置',
          },
          condition: (target) => {
            return !!target.getProps().getPropValue('seriesColumn.series.labelForAxis');
          },
          setter: {
            componentName: 'SelectSetter',
            props: {
              placeholder: '选择位置',
              options: [
                {
                  title: '顶部',
                  value: 'top',
                },
                {
                  title: '底部',
                  value: 'bottom',
                },
              ],
            },
          },
        },
        {
          name: 'series.symbolPoint',
          title: '数值标签',
          display: 'inline',
          isRequired: false,
          defaultValue: true,
          setter: 'BoolSetter',
        },
        {
          name: 'series.showSite',
          title: '标签位置',
          display: 'inline',
          isRequired: false,
          condition: (target) => {
            return !!target.getProps().getPropValue('seriesColumn.series.symbolPoint');
          },
          defaultValue: 'horizontal',
          setter: {
            componentName: 'SelectSetter',
            props: {
              placeholder: '选择位置',
              options: [
                {
                  title: '水平分布',
                  value: 'horizontal',
                },
                {
                  title: '垂直分布',
                  value: 'vertical',
                },
                {
                  title: '中心',
                  value: 'inside',
                },
                {
                  title: '左侧',
                  value: 'left',
                },
                {
                  title: '右侧',
                  value: 'right',
                },
                {
                  title: '顶部',
                  value: 'top',
                },
                {
                  title: '底部',
                  value: 'bottom',
                },
                {
                  title: '内部左侧',
                  value: 'insideLeft',
                },
                {
                  title: '内部右侧',
                  value: 'insideRight',
                },
              ],
            },
          },
        },
        {
          name: 'series.valueColor',
          title: '数值颜色',
          display: 'inline',
          isRequired: false,
          condition: (target) => {
            return !!target.getProps().getPropValue('seriesColumn.series.symbolPoint');
          },
          defaultValue: '#1D2129',
          setter: 'ColorSetter',
        },
        // 数据轴数据列
        {
          name: 'series.chartColumn',
          type: 'field',
          display: 'plain',
          title: {
            label: '数据轴数据列',
          },
          setter: chartColumnItems,
        },
      ],
    },
  },
};

import { auxiliaryLineItems } from "./auxiliaryLine";

export const advancedItems = {
  componentName: 'ObjectSetter',
  props: {
    config: {
      items: [
        {
          name: 'canExport',
          title: '可否导出',
          setter: 'BoolSetter',
        },
        {
          name: 'auxiliaryLine',
          title: '辅助线',
          setter: 'BoolSetter',
        },
        {
          name: 'auxiliaryLineConfig',
          title: '辅助线配置',
          type: 'field',
          display: 'plain',
          condition: (target) => {
            return !!target.getProps().getPropValue('advanced.auxiliaryLine');
          },
          setter: auxiliaryLineItems,
        },
      ],
    },
  },
};

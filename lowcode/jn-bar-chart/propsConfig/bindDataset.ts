import { handleDataPropsSet } from '../service/serviceInit';

export const bindDatasetItems = [
  {
    name: 'specificDataset.bindDataset',
    display: 'inline',
    title: '数据绑定',
    extraProps: {
      setValue: (target, value) => {
        if (value) {
          handleDataPropsSet(target, value);
        }

        return value;
      },
    },
    setter: {
      componentName: 'JNSelectSetter',
      props: {
        mode: 'single',
        showSearch: true,
        filterType: 'List',
      },
    },
  },
];

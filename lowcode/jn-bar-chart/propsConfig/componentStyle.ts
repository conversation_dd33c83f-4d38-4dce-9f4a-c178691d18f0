export const componentStyleItems = [
  {
    name: 'componentStyle.chartTitleShow',
    title: '标题显示',
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.chartTitle',
    title: '图表名称',
    setter: 'StringSetter',
  },
  {
    name: 'componentStyle.showMoreText',
    title: '更多显示',
    setter: {
      componentName: 'MixedSetter',
      props: {
        setters: ['StringSetter', { componentName: 'IconFontSetter', props: { type: 'node' } }],
      },
    },
  },
  {
    name: 'componentStyle.linkText',
    title: '链接地址',
    setter: 'StringSetter',
    condition: (target) => {
      return !!target.getProps().getPropValue('componentStyle.showMoreText');
    },
  },
  {
    name: 'componentStyle.chartHeight',
    title: '图表高度',
    defaultValue: 400,
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.legendShow',
    title: '图例展示',
    setter: 'BoolSetter',
  },
  {
    name: 'componentStyle.categoryShowType',
    title: '类目显示',
    setter: {
      componentName: 'CustomRadioGroupSetter',
      props: {
        dataSource: [
          { label: '平铺标签', value: 'tiled_tags' },
          { label: '平铺复选', value: 'tiled_checkbox' },
          {
            label: '下拉菜单',
            value: 'dropdown_select',
          },
          {
            label: '无展示',
            value: '',
          },
        ],
      },
    },
  },
  {
    name: 'componentStyle.barWidth',
    title: '柱状宽度',
    setter: 'NumberSetter',
  },
  {
    name: 'componentStyle.chartGridTop',
    title: '图表上边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 20,
    },
  },
  {
    name: 'componentStyle.chartGridBottom',
    title: '图表下边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.chartGridLeft',
    title: '图表左边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.chartGridRight',
    title: '图表右边距',
    setter: {
      componentName: 'NumberSetter',
      props: {
        units: '%',
      },
      initialValue: 10,
    },
  },
  {
    name: 'componentStyle.xAxisShowType',
    title: 'X轴显示',
    defaultValue: 'value',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          { title: '类目', value: 'category' },
          { title: '数据', value: 'value' },
        ],
      },
      // componentName: 'CustomRadioGroupSetter',
      // props: {
      //   dataSource: [
      //     { label: '类目', value: 'category' },
      //     { label: '数据', value: 'value' },
      //   ],
      // },
    },
    extraProps: {
      setValue: (target, value) => {
        if (value === 'category') {
          target.getProps().setPropValue('componentStyle.yAxisShow', 'value');
        } else {
          target.getProps().setPropValue('componentStyle.yAxisShow', 'category');
        }
      },
    },
  },
  {
    name: 'componentStyle.yAxisShowType',
    title: 'Y轴显示',
    defaultValue: 'category',
    setter: {
      componentName: 'RadioGroupSetter',
      props: {
        options: [
          { title: '类目', value: 'category' },
          { title: '数据', value: 'value' },
        ],
      },
      // componentName: 'CustomRadioGroupSetter',
      // props: {
      //   dataSource: [
      //     { label: '类目', value: 'category' },
      //     { label: '数据', value: 'value' },
      //   ],
      // },
    },
    extraProps: {
      setValue: (target, value) => {
        if (value === 'category') {
          target.getProps().setPropValue('componentStyle.xAxisShow', 'value');
        } else {
          target.getProps().setPropValue('componentStyle.xAxisShow', 'category');
        }
      },
    },
  },
];

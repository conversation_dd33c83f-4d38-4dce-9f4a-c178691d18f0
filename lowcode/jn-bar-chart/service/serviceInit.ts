import { ceil, get } from 'lodash';
import { defaultSeriesColorList } from '../constants/defaultChartConstants';
import { requestBjxDataset, requestTooltipsBjxDataset } from '../../service/chart';


let headListSelectOption:any = {} // 获取最新的数据集headList

export const getSelectOption = (bindDataset) => {
  if(bindDataset && headListSelectOption[bindDataset]){
    return headListSelectOption[bindDataset]
  }else if(!bindDataset){
    return []
  }else{
    requestBjxDataset(bindDataset).then(data => {
      headListSelectOption[bindDataset] = get(data, 'data.result.data.headList');

    })
  }
}

export const handleDataPropsSet = async (target, bindDataset) => {
  const tooltipResponse = await requestTooltipsBjxDataset(bindDataset);
  const bjxDataResponse = await requestBjxDataset(bindDataset);

  // console.log('[TooltipResponse]', tooltipResponse);
  // console.log('[BjxDataResponse]', bjxDataResponse);

  const tooltipFields = get(tooltipResponse, 'data.result');
  const bjxFields = get(bjxDataResponse, 'data.result.data.headList');

  // console.log('[TooltipFields]', tooltipFields)
  // console.log('[BjxFields]', bjxFields)

  // LowCode 配置的数据列需要将"更多工具提示"含有的字段移除
  const tooltipKeys = tooltipFields?.map((fieldItem) => fieldItem?.key);
  const bjxFieldsFilter = bjxFields?.filter((fieldItem) => {
    return !tooltipKeys?.includes(fieldItem?.fieldMeta?.key);
  });

  // console.log('[BjxFieldsFilter]', bjxFieldsFilter);

  // 配置逻辑
  const resultSeriesColumn = [];
  // HeadList 第一个字段为日期/品类/其余文本，但仅用于X轴渲染，实际图表渲染需要将此去除
  const bjxFieldsWithoutDate = bjxFieldsFilter?.slice(1);

  // 初始化
  target.parent.setPropValue('seriesColumn.series.chartColumn', []);
  target.parent.setPropValue('advanced.auxiliaryLineConfig', []);

  let index = 1;

  for (const item of bjxFieldsWithoutDate) {
    const columnItem = {
      dataField: item.key,
      nickName: item.title,
      valueColor: defaultSeriesColorList[ceil((index - 1) % 5, 0)],
      defaultShow: true,
      isShowByDelete: false,
      itemTooltipShow: true,
    };

    resultSeriesColumn.push(columnItem as never);

    index += 1;
  }

  target.parent.setPropValue('seriesColumn.series.chartColumn', resultSeriesColumn);
};

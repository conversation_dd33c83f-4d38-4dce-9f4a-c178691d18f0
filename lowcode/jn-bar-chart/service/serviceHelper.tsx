import { DEFAULT_DATA_FORMAT_TYPE } from "../../common/config/dataFormatType";

export const chartColumnItems = {
  componentName: 'ArraySetter',
  props: {
    itemSetter: {
      componentName: 'ObjectSetter',
      props: {
        config: {
          items: [
            {
              name: 'dataField',
              title: '数据字段',
              display: 'inline',
              isRequired: false,
              setter: 'StringSetter',
            },
            {
              name: 'dataFormatType',
              title: '数据格式',
              display: 'inline',
              setter: {
                componentName: 'SelectSetter',
                props: {
                  options: DEFAULT_DATA_FORMAT_TYPE,
                },
              },
            },
            {
              name: 'nickName',
              title: '柱名称',
              display: 'inline',
              isRequired: true,
              setter: 'StringSetter',
            },
            {
              name: 'valueColor',
              title: '柱颜色',
              display: 'inline',
              isRequired: true,
              setter: 'ColorSetter',
            },
            {
              name: 'areaLinearColor',
              title: '显示渐变',
              display: 'inline',
              defaultValue: false,
              setter: {
                componentName: 'RadioGroupSetter',
                props: {
                  options: [
                    {
                      title: '线性渐变',
                      value: 'linear',
                    },
                    {
                      title: '径向渐变',
                      value: 'radial',
                    },
                  ],
                },
              },
            },
            {
              name: 'linearType',
              title: '线性渐变类型',
              display: 'inline',
              setter: {
                componentName: 'RadioGroupSetter',
                props: {
                  options: [
                    {
                      title: '横向渐变',
                      value: 'horizontal',
                    },
                    {
                      title: '纵向渐变',
                      value: 'vertical',
                    },
                  ],
                },
              },
            },
            {
              name: 'areaLinearColorStart',
              title: '渐变起始色',
              display: 'inline',
              setter: 'ColorSetter',
            },
            {
              name: 'areaLinearColorEnd',
              title: '渐变结束色',
              display: 'inline',
              setter: 'ColorSetter',
            },
            {
              name: 'defaultShow',
              title: '图例控制',
              defaultValue: true,
              display: 'inline',
              setter: 'BoolSetter',
              condition: (target) => {
                return !!target.getProps().getPropValue('componentStyle.legendShow');
              },
            },
          ],
        },
      },
    },
  },
};
